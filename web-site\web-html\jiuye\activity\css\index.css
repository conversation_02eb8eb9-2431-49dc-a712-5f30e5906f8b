/* banner */
.bannerBox {
    height: 220px;
}

.bannerBox .hd {
    position: absolute;
    bottom: 0;
    margin-left: 50%;
    z-index: 10;
}

.bannerBox .hd ul {
    text-align: center;
    display: block;
    margin: 0 auto;
}

.bannerBox .hd li {
    display: inline-block;
    width: 9px;
    height: 9px;
    overflow: hidden;
    margin-right: 5px;
    text-indent: -999px;
    cursor: pointer;
    background: #e2e2e2;
    border-radius: 50px;
}

.bannerBox .hd li.on {
    background: #0052d9;
}

/* banner 轮播 */
.bannerSlide {
    width: 100% !important;
    height: 260px;
    top: 0;
    left: 0;
    min-width: 1400px;
    position: absolute !important;
}

.bannerSlide img {
    display: block;
    width: 100% !important;
    height: 100%;
}

.conAuto2 {
    width: 1400px;
    min-width: 1400px;
    margin-left: auto;
    margin-right: auto;
}

.bannerBox .numBox {
    width: 1160px;
    height: 120px;
    position: absolute;
    bottom: 0;
    margin-right: 50%;
    right: -206px;
    background: url(../image/new_zhdBannerPic.png) no-repeat right bottom;
}

.bannerBox .numBox li {
    padding-left: 151px;
    width: 150px;
    height: 120px;
}

.bannerBox .numBox li.line {
    width: 1px;
    height: 70px;
    margin-top: 25px;
    background: #449ce9;
    padding: 0;
}

.bannerBox .numBox li .nump {
    font-size: 20px;
    color: #fff;
    padding: 15px 0 4px;
}

.bannerBox .numBox li .nump span {
    font-size: 38px;
    color: #fff720;
    font-weight: bold;
}

.bannerBox .numBox li .textp {
    font-size: 20px;
    font-weight: bold;
    color: #fff;
}

/* 培训订单详情页面样式优化 */
.training-order-detail {
    background: #f8f9fa;
    min-height: 100vh;
}

.fee-text {
    color: #ff6b35;
    font-weight: 600;
    font-size: 16px;
}

.fee-text.free {
    color: #52c41a;
}

.action-buttons {
    display: flex;
    gap: 10px;
    align-items: center;
}

.action-btn {
    padding: 8px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 6px;
    color: #fff;
    text-decoration: none;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
    color: #fff;
}

/* 订单详情卡片样式 */
.topModule {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 30px;
    position: relative;
}

.leftBox {
    padding: 30px;
    padding-right: 370px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
}

.parkName {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 15px;
    line-height: 1.3;
}

.tebsBox {
    margin-bottom: 20px;
}

.tebsBox span {
    display: inline-block;
    padding: 6px 16px;
    margin-right: 12px;
    background: #fff;
    border: 1px solid #e1e8ed;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tebsBox span:first-child {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-color: transparent;
}

.infop {
    font-size: 15px;
    color: #555;
    margin-bottom: 12px;
    line-height: 1.6;
    display: flex;
    align-items: center;
}

.infop:before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    margin-right: 8px;
}

.linkBox {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.linkBox p {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.linkBox span {
    color: #333;
    font-weight: 500;
    margin-left: 8px;
}

.bottomBoxs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
    padding: 25px;
    background: #fff;
    border-radius: 8px;
    margin-top: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btnCont1 {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #fff;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.btnCont1:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(82, 196, 26, 0.4);
    color: #fff;
}

.btnCont3 {
    background: #f0f0f0;
    color: #999;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: not-allowed;
}

.btnCont4 {
    background: #ff4d4f;
    color: #fff;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: not-allowed;
}

.enlistNumber {
    font-size: 14px;
    color: #666;
    background: #f8f9fa;
    padding: 8px 16px;
    border-radius: 6px;
}

.enlistNumber span {
    color: #667eea;
    font-weight: 600;
    font-size: 16px;
}

.chargeTypeName {
    font-size: 18px;
    font-weight: 700;
    color: #ff6b35;
    background: #fff5f0;
    padding: 10px 20px;
    border-radius: 8px;
    border: 2px solid #ffe0d6;
}

/* 内容区域样式 */
.leftBoxs {
    width: 65%;
    padding-right: 30px;
}

.rightBoxs {
    width: 33%;
}

/* 右侧信息卡片 */
.rightInfoCard {
    position: absolute;
    right: 30px;
    top: 30px;
    width: 320px;
    z-index: 10;
    max-height: calc(100vh - 100px);
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
}

.rightInfoCard::-webkit-scrollbar {
    width: 4px;
}

.rightInfoCard::-webkit-scrollbar-track {
    background: transparent;
}

.rightInfoCard::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 2px;
}

.rightInfoCard::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

.signup-status-card,
.highlights-card,
.contact-card {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    border: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    animation: slideInRight 0.6s ease-out;
}

.signup-status-card {
    animation-delay: 0.1s;
}

.highlights-card {
    animation-delay: 0.2s;
}

.contact-card {
    animation-delay: 0.3s;
}

.signup-status-card:hover,
.highlights-card:hover,
.contact-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.signup-status-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
}

.status-header {
    padding: 20px 20px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
}

.status-badge {
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.status-content {
    padding: 10px 20px 25px;
}

.participants-progress {
    text-align: center;
    margin-bottom: 20px;
}

.progress-info {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 10px;
}

.current {
    color: #fff;
}

.separator {
    margin: 0 5px;
    opacity: 0.7;
}

.total {
    opacity: 0.8;
}

.unit {
    font-size: 14px;
    margin-left: 5px;
    opacity: 0.8;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    opacity: 0.8;
}

.price-info {
    text-align: center;
    padding: 15px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 20px;
}

.price-label {
    font-size: 12px;
    opacity: 0.8;
    margin-bottom: 5px;
}

.price-value {
    font-size: 20px;
    font-weight: 700;
    color: #fff;
}

.signup-actions {
    text-align: center;
}

.signup-btn-main {
    width: 100%;
    background: #fff;
    color: #667eea;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.signup-btn-main:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.action-row {
    display: flex;
    gap: 10px;
}

.action-btn-small {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    backdrop-filter: blur(10px);
}

.action-btn-small:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* 培训亮点卡片 */
.highlights-card h3,
.contact-card h3 {
    margin: 0;
    padding: 20px 20px 15px;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
}

.highlights-list {
    padding: 15px 20px 20px;
}

.highlight-item {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 8px 0;
}

.highlight-item:last-child {
    margin-bottom: 0;
}

.highlight-icon {
    font-size: 16px;
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.highlight-text {
    font-size: 14px;
    color: #555;
    font-weight: 500;
}

/* 联系信息卡片 */
.contact-info {
    padding: 15px 20px 20px;
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-icon {
    font-size: 16px;
    margin-right: 12px;
    width: 20px;
    text-align: center;
}

.contact-details {
    flex: 1;
}

.contact-label {
    font-size: 12px;
    color: #999;
    margin-bottom: 2px;
}

.contact-value {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

/* 按钮图标样式 */
.btn-icon {
    font-size: 14px;
}

.btn-text {
    font-size: inherit;
}

/* 状态徽章样式 */
.status-badge.status-published {
    background: rgba(82, 196, 26, 0.2);
    color: #52c41a;
}

.status-badge.status-ongoing {
    background: rgba(24, 144, 255, 0.2);
    color: #1890ff;
}

.status-badge.status-completed {
    background: rgba(114, 46, 209, 0.2);
    color: #722ed1;
}

.status-badge.status-cancelled {
    background: rgba(255, 77, 79, 0.2);
    color: #ff4d4f;
}

.status-badge.status-draft {
    background: rgba(217, 217, 217, 0.2);
    color: #999;
}

.textStyles {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 2px solid #667eea;
    position: relative;
}

.textStyles:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.infoBoxs {
    background: #fff;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    line-height: 1.8;
    font-size: 15px;
    color: #555;
}

.infoBoxs p {
    margin-bottom: 15px;
}

.infoBoxs ul {
    padding-left: 20px;
    margin-bottom: 15px;
}

.infoBoxs li {
    margin-bottom: 8px;
    position: relative;
}

.infoBoxs li:before {
    content: "▸";
    color: #667eea;
    font-weight: bold;
    position: absolute;
    left: -15px;
}

/* 右侧边栏样式 */
.codeBox {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    text-align: center;
}

.changeBox {
    color: #667eea;
    font-size: 13px;
    text-decoration: none;
    padding: 4px 12px;
    border: 1px solid #667eea;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.changeBox:hover {
    background: #667eea;
    color: #fff;
}

/* 推荐列表优化 */
.bd {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.bd ul {
    padding: 0;
    margin: 0;
}

.bd ul li {
    margin: 0;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
}

.bd ul li:last-child {
    border-bottom: none;
}

.bd ul li:hover {
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
    transform: translateX(5px);
}

.bd ul li a {
    font-size: 15px;
    font-weight: 600;
    color: #333;
    line-height: 1.4;
    text-decoration: none;
    display: block;
    margin-bottom: 8px;
}

.bd ul li a:hover {
    color: #667eea;
}

.bd ul li .activityTheme span {
    font-size: 13px;
    color: #888;
    background: #f5f5f5;
    padding: 2px 8px;
    border-radius: 4px;
}

.bd ul li .timeBoxs {
    font-size: 13px;
    color: #999;
    margin-top: 8px;
}

.bd ul li .timeBoxs span {
    color: #ff6b35;
    font-weight: 600;
}

/* 加载动画样式 */
.loading-content {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* Toast 消息样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    z-index: 10000;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-success {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.toast-error {
    background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
}

.toast-info {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

/* 错误状态样式 */
.error-content {
    text-align: center;
    padding: 40px 20px;
    color: #ff4d4f;
}

.error-content .error-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.error-content p {
    margin: 10px 0;
    font-size: 16px;
}

.error-content .retry-btn {
    background: #ff4d4f;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    margin-top: 15px;
    transition: all 0.3s ease;
}

.error-content .retry-btn:hover {
    background: #ff7875;
    transform: translateY(-2px);
}

/* 订单详情卡片样式 */
.topModule {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 30px;
}

.leftBox {
    padding: 30px;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
}

.parkName {
    font-size: 28px;
    font-weight: 700;
    color: #1a1a1a;
    margin-bottom: 15px;
    line-height: 1.3;
}

.tebsBox {
    margin-bottom: 20px;
}

.tebsBox span {
    display: inline-block;
    padding: 6px 16px;
    margin-right: 12px;
    background: #fff;
    border: 1px solid #e1e8ed;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.tebsBox span:first-child {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border-color: transparent;
}

.infop {
    font-size: 15px;
    color: #555;
    margin-bottom: 12px;
    line-height: 1.6;
    display: flex;
    align-items: center;
}

.infop:before {
    content: "•";
    color: #667eea;
    font-weight: bold;
    margin-right: 8px;
}

.linkBox {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin: 20px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.linkBox p {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.linkBox span {
    color: #333;
    font-weight: 500;
    margin-left: 8px;
}

.bottomBoxs {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 15px;
    padding: 25px;
    background: #fff;
    border-radius: 8px;
    margin-top: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btnCont1 {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
    color: #fff;
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.btnCont1:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(82, 196, 26, 0.4);
    color: #fff;
}

.btnCont3 {
    background: #f0f0f0;
    color: #999;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: not-allowed;
}

.btnCont4 {
    background: #ff4d4f;
    color: #fff;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 500;
    cursor: not-allowed;
}

.enlistNumber {
    font-size: 14px;
    color: #666;
    background: #f8f9fa;
    padding: 8px 16px;
    border-radius: 6px;
}

.enlistNumber span {
    color: #667eea;
    font-weight: 600;
    font-size: 16px;
}

.chargeTypeName {
    font-size: 18px;
    font-weight: 700;
    color: #ff6b35;
    background: #fff5f0;
    padding: 10px 20px;
    border-radius: 8px;
    border: 2px solid #ffe0d6;
}

.schedule-table {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #e9ecef;
}

.schedule-info {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.schedule-info p {
    margin: 0;
    padding: 12px;
    background: #fff;
    border-radius: 6px;
    border-left: 4px solid #667eea;
    font-size: 14px;
    line-height: 1.5;
}

.schedule-info strong {
    color: #333;
    font-weight: 600;
    display: inline-block;
    min-width: 80px;
}

.schedule-info span {
    color: #555;
}

.schedule-row {
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 14px;
    line-height: 1.5;
}

.schedule-row:last-child {
    border-bottom: none;
}

.schedule-row strong {
    color: #333;
    font-weight: 600;
}

/* 推荐列表样式优化 */
.bd ul li {
    margin-bottom: 15px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.bd ul li:hover {
    background: #e6f7ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.bd ul li a {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    line-height: 1.4;
}

.bd ul li a:hover {
    color: #1890ff;
}

.bd ul li .activityTheme span {
    font-size: 12px;
    color: #666;
}

.bd ul li .timeBoxs {
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.bd ul li .timeBoxs span {
    color: #ff6b35;
    font-weight: 500;
}

/* 底部信息条样式 */
.bottomBar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px 0;
    margin-top: 40px;
}

.bottomContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.bottomLogo .logoText {
    font-size: 24px;
    font-weight: 600;
    color: #fff;
    margin-right: 10px;
}

.bottomLogo .logoSubtext {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .conAuto2 {
        width: 95%;
        min-width: auto;
        padding: 0 20px;
    }
}

@media (max-width: 1200px) {
    .leftBoxs {
        width: 60%;
        padding-right: 20px;
    }

    .rightBoxs {
        width: 38%;
    }

    .rightInfoCard {
        width: 280px;
        right: 20px;
    }

    .parkName {
        font-size: 24px;
    }

    .schedule-info {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .clearfix .fl.leftBoxs,
    .clearfix .fr.rightBoxs {
        float: none;
        width: 100%;
        margin-bottom: 20px;
        padding-right: 0;
    }

    .rightInfoCard {
        position: static;
        width: 100%;
        margin-top: 20px;
        right: auto;
    }

    .leftBox {
        padding: 20px;
        padding-right: 20px;
    }

    .parkName {
        font-size: 20px;
        text-align: center;
    }

    .tebsBox {
        text-align: center;
    }

    .tebsBox span {
        margin: 5px;
        display: inline-block;
    }

    .infop {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
    }

    .infop:before {
        margin-bottom: 5px;
    }

    .linkBox {
        padding: 15px;
    }

    .bottomContent {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .action-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }

    .bottomBoxs {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .bottomBoxs .fl,
    .bottomBoxs .fr {
        float: none;
        margin: 0;
        width: 100%;
    }

    .btnCont1,
    .btnCont3,
    .btnCont4 {
        width: 100%;
        text-align: center;
        margin-bottom: 10px;
    }

    .schedule-info {
        grid-template-columns: 1fr;
    }

    .textStyles {
        font-size: 18px;
        text-align: center;
    }

    .infoBoxs {
        padding: 20px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .leftBox {
        padding: 15px;
    }

    .parkName {
        font-size: 18px;
    }

    .tebsBox span {
        font-size: 12px;
        padding: 4px 12px;
    }

    .infop {
        font-size: 14px;
    }

    .btnCont1,
    .btnCont3,
    .btnCont4 {
        padding: 10px 20px;
        font-size: 14px;
    }

    .action-btn {
        padding: 6px 12px;
        font-size: 12px;
    }

    .infoBoxs {
        padding: 15px;
        font-size: 13px;
    }

    .textStyles {
        font-size: 16px;
    }
}

.bannerBox .numBox li.icon1 {
    background: url(../image/new_zhdBannerPic2.png) no-repeat 54px center;
    background-size: 80px 80px;
}

.bannerBox .numBox li.icon2 {
    background: url(../image/new_zhdBannerPic3.png) no-repeat 54px center;
    background-size: 80px 80px;
}

.bannerBox .numBox li.icon3 {
    background: url(../image/new_zhdBannerPic4.png) no-repeat 54px center;
    background-size: 80px 80px;
}

.pageBg {
    width: 100%;
    min-width: 1400px;
    min-height: 892px;
    background: url(../image/zhd_bg.png)  top center;
    overflow: visible;
}

/* 标题 */
.contentTitle {
    font-size: 30px;
    font-weight: bold;
    color: #333;
    padding-bottom: 18px;
    background: url(../../public/images/icons/titleStyleLine.png) no-repeat center bottom;
}

.contentTitle.contentTitle2 {
    background: url(../image/titleStyleLine2.png) no-repeat center bottom;
}

.contentTitle em {
    color: #0052d9;
    font-size: 30px;
    font-weight: bold;
}

.xg {
    color: #0052d9;
    font-size: 7.76px;
    line-height: 15px;
    margin-top: 10px;
}

.moreBtn {
    height: 30px;
    padding-right: 12px;
    border-radius: 30px;
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
}

.moreBtn span {
    line-height: 30px;
    padding-right: 20px;
    padding-left: 11px;
    background: url(../../public/images/icons/icon_more.png) no-repeat right center;
}

.moreBtn:hover {
    animation: moreBtn 0.5s 1 forwards;
    -webkit-animation: moreBtn 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn {
    from {
        background-position: 0;
    }

    to {
        background-position: 71px;
    }
}

/* 入口 */
.enterOut {
    width: 1430px;
    margin-top: 40px;
    position: relative;
}

.enterOut a {
    width: 446px;
    height: 300px;
    margin-right: 30px;
    box-shadow: 0px 0px 10px 0px rgba(0, 82, 217, 0.14);
}

.enter1 {
    background: url(../image/zhd_enter1.png) no-repeat;
}

.enter2 {
    background: url(../image/zhd_enter2.png) no-repeat;
}

.enter3 {
    background: url(../image/zhd_enter3.png) no-repeat;
}

.enter4 {
    background: url(../image/zhd_enter4.png) no-repeat;
}

.enter1 em,
.enter2 em,
.enter3 em,
.enter4 em {
    display: block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    color: #fff;
    border-radius: 50px;
    background: url(../image/zhd_btn1.png) no-repeat;
    margin: 240px 0 0 107px;
}

.enter2 em {
    background: url(../image/zhd_btn2.png) no-repeat;
    margin: 240px auto 0;
}

.enter3 em {
    margin: 240px auto 0;
}

.enter4 em {
    height: 35px;
    background: url(../image/zhd_btn4.png) no-repeat;
    margin: 240px auto 0;
}

.enterOut a:hover em {
    animation: rotate 0.3s ease infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0);
    }

    20% {
        transform: rotate(-2deg);
    }

    60% {
        transform: rotate(0);
    }

    80% {
        transform: rotate(2deg);
    }

    100% {
        transform: rotate(0);
    }
}

/* 活动动态 */
.cyhdOut {
    border: 1px solid #eaf2ff;
    width: 683px;
    height: 400px;
    border-radius: 8px;
    margin-top: 20px;
    background: #fff;
}

.activityList li {
    width: 605px;
    height: 154px;
    background: url(../image/new_zhdJqhdBg.png) no-repeat center top;
    border-radius: 6px;
    margin-top: 20px;
    margin-left: 20px;
    padding: 16px 20px 0;
}

.activityList li:last-child {
    margin-right: 0;
}

.activityList li:hover {
    background: url(../image/new_zhdJqhdBgOn.png) no-repeat center top;
    box-shadow: 0px 0px 16px 0px rgba(0, 82, 217, 0.2);
}

.activityList li .arrs {
    display: block;
    width: 60px;
    height: 60px;
    top: 1px;
    right: 1px;
}

.spanBox0 {
    background: url(../image/new_zhdJqhdTab1.png);
}

.spanBox1 {
    background: url(../image/new_zhdJqhdTab2.png);
}

.spanBox2 {
    background: url(../image/new_zhdJqhdTab3.png);
}

.activityList li .baseName {
    font-size: 20px;
    line-height: 26px;
    height: 26px;
    width: 553px;
    margin-bottom: 14px;
}

.activityList li:hover .baseName {
    color: #0052d9;
}

.activityList li .time {
    padding-left: 25px;
    background: url(../image/new_zhdTime.png) no-repeat left center;
    line-height: 30px;
    height: 30px;
    font-size: 16px;
    color: #666;
}

.activityList li .address {
    padding-left: 25px;
    background: url(../image/new_zhdAddress.png) no-repeat left center;
    line-height: 30px;
    height: 30px;
    font-size: 16px;
    color: #666;
    margin-bottom: 10px;
}

.activityList li .type>span {
    height: 30px;
    line-height: 30px;
    padding: 0 15px;
    background: #eaf2ff;
    text-align: center;
    color: #0052d9;
    border-radius: 0 50px 50px 50px;
}

.activityList li .type p {
    height: 30px;
    border-radius: 30px;
    background: #fff;
    color: #0052d9;
    width: 100px;
    border: 1px solid #eaf2ff;
}

.activityList li .type p span {
    display: block;
    line-height: 30px;
    padding-left: 12px;
    background: url(../image/new_zhdArr.png) no-repeat 80px center;
}

.activityList li:hover .type p span {
    background: url(../../public/images/icons/icon_more.png) no-repeat 80px center;
}

.activityList li:hover .type p {
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
    color: #fff;
}

.activityList li:hover .type p {
    animation: moreBtn2 0.5s 1 forwards;
    -webkit-animation: moreBtn2 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn2 {
    from {
        background-position: 0;
    }

    to {
        background-position: 100px;
    }
}

/* 活动回顾 */
.rightBox {
    width: 675px;
    height: 450px;
    background: url(../image/zhd_bg2.png) no-repeat center bottom;
    margin-top: 40px;
    position: relative;
    padding: 17px 5px 5px;
}

.rightBox>.title {
    position: absolute;
    width: 249px;
    height: 40px;
    line-height: 40px;
    font-size: 16px;
    color: #fff;
    text-align: center;
    font-weight: bold;
    background: url(../image/zhd_title.png) no-repeat top center;
    z-index: 100;
    left: 218px;
    top: 0;
}

.rightBox .moreTxt {
    display: block;
    width: 50px;
    color: #0052d9;
    background: url(../../policy/images/policySIcon10.png) no-repeat right center;
    margin: 7px auto 0;
}

.hd {
    width: 100%;
    position: absolute;
    left: 0;
    /* top: 0; */
}

.hd a {
    position: absolute;
    height: 30px;
    width: 30px;
    top: 172px;
}

.hd a.prev {
    left: 20px;
    background: url(../../policy/images/index_dspArr1.png) no-repeat center;
    background-size: 100% 100%;
}

.hd a.prev:hover {
    background: url(../../policy/images/policySIcon1On.png) no-repeat center;
    background-size: 100% 100%;
}

.hd a.next {
    right: 20px;
    background: url(../../policy/images/index_dspArr2.png) no-repeat center;
    background-size: 100% 100%;
}

.hd a.next:hover {
    background: url(../../policy/images/policySIcon2On.png) no-repeat center;
    background-size: 100% 100%;
}

.rightBox .tempWrap {
    border-radius: 8px;
}

.rightBox .videoBox a {
    width: 675px !important;
    height: 450px !important;
    border-radius: 9px;
    overflow: hidden;
    position: relative;
}

.rightBox .videoBox .title {
    position: absolute;
    width: 525px;
    height: 50px;
    line-height: 50px;
    padding: 0 10px 0 10px;
    background: url(../image/zhd_titleBg.png) repeat;
    z-index: 10;
    bottom: 10px;
    left: 10px;
    color: #fff;
    font-size: 18px;
    border-radius: 8px;
}

.rightBox .videoBox a img {
    width: 675px !important;
    height: 450px !important;
    display: block;
}
.rightBox .hd{
    background: url(../image/zhd_titleBg.png) repeat;
    position: absolute;
    bottom: 0;
    left: 0;
    border-radius: 0 0 8px 8px;
    height: 90px;
    padding: 10px 0 0 10px;
    box-sizing: border-box;
    overflow: hidden;
}
.rightBox .hd li{
    width: 101px !important;
    height: 66px;
    border-radius: 5px;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.281);
    margin-right: 5px;
}
.rightBox .hd li.on{
    border: 2px solid #fff;
}
.rightBox .hd li img{
    display: block;
    width: 100%;
    height: 100%;
}
.hdhg{
    width: 675px;
    height: 450px;
}
.rightBox .listBox{
    width: 267px;
    height: 452px;
    background: url(../image/zhd_listbg.png) no-repeat left center;
    position: absolute;
    right: 0;
    top: 0;
    overflow-y: scroll;
}
.yearList {
    margin-top: 33px;
    margin-left: 40px;
}
.yearList li{
    width: 210px;
}
.yearList li .years,.months{
    width: 190px;
    height: 39px;
    line-height: 39px;
    background: url(../image/zhd_libg.png);
    border: 1px solid #2e959c;
    border-radius: 8px;
    margin-bottom: 10px;
    padding-left: 18px;
    font-size: 18px;
    font-weight: bold;
    color: #fff;
    position: relative;
    cursor: pointer;
}
.yearList li .years::after,.months::after{
    position: absolute;
    top: 16px;
    right: 18px;
    content: "";
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 10px solid #fff;
}
.yearList li .years.on{
    background-image: -moz-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    background-image: -ms-linear-gradient( 0deg, rgb(0,82,217) 0%, rgb(19,176,190) 100%);
    border: none;
    width: 192px;
    height: 41px;
    line-height: 41px;
}
.yearList li .years.on::after{
    position: absolute;
    top: 6px;
    right: 18px;
    content: "";
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 10px solid #fff;
    border-top: 10px solid transparent;
}
.months{
    font-weight: 500;
    font-size: 16px;
}
.months.on{
    background-image: -moz-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
    background-image: -ms-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
}
.yearList li .months.on::after{
    position: absolute;
    top: 6px;
    right: 18px;
    content: "";
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 10px solid #fff;
    border-top: 10px solid transparent;
}
.hds{
    width: 178px;
    height: 48px;
    line-height: 16px;
    background: url(../image/zhd_libg.png);
    border: 1px solid #2e959c;
    padding: 5px 10px;
    border-radius: 8px;
    margin-bottom: 10px;
    margin-left: 10px;
    font-size: 12px;
    color: #fff;
    position: relative;
    cursor: pointer;
}
.hds.on{
    background-image: -moz-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
    background-image: -webkit-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
    background-image: -ms-linear-gradient( 0deg, rgba(0,82,217,0.8) 0%, rgba(19,176,190,0.8) 100%);
}

/* 培训订单样式 */
.trainingOrderOut {
    width: 100%;
    margin-top: 20px;
    display: block;
    visibility: visible;
}

.trainingOrderList {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    padding: 0;
    margin: 0;
    list-style: none;
}

.trainingOrderList li {
    width: 100%;
    min-height: 200px;
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    position: relative;
    box-sizing: border-box;
    display: block;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.trainingOrderList li:last-child {
    margin-right: 0;
}

.trainingOrderList li:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 82, 217, 0.15);
    border-color: #007bff;
}

/* 订单状态标签 */
.trainingOrderList li .arrs {
    display: block;
    width: 60px;
    height: 60px;
    top: 1px;
    right: 1px;
}

.orderStatus0 {
    background: #999;
    border-radius: 0 0 0 30px;
}

.orderStatus0::after {
    content: "草稿";
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    color: #fff;
    font-weight: bold;
}

.orderStatus1 {
    background: #52c41a;
    border-radius: 0 0 0 30px;
}

.orderStatus1::after {
    content: "发布";
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    color: #fff;
    font-weight: bold;
}

.orderStatus2 {
    background: #faad14;
    border-radius: 0 0 0 30px;
}

.orderStatus2::after {
    content: "进行中";
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    color: #fff;
    font-weight: bold;
}

.orderStatus3 {
    background: #1890ff;
    border-radius: 0 0 0 30px;
}

.orderStatus3::after {
    content: "已完成";
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    color: #fff;
    font-weight: bold;
}

.orderStatus4 {
    background: #f5222d;
    border-radius: 0 0 0 30px;
}

.orderStatus4::after {
    content: "已取消";
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 10px;
    color: #fff;
    font-weight: bold;
}

/* 订单头部 */
.trainingOrderList li .orderHeader {
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 12px;
}

.trainingOrderList li .orderTitle {
    font-size: 16px;
    line-height: 1.4;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.trainingOrderList li:hover .orderTitle {
    color: #007bff;
}

.trainingOrderList li .orderFee {
    font-size: 18px;
    font-weight: bold;
    color: #ff6b35;
    line-height: 1;
}

/* 订单信息 */
.trainingOrderList li .orderType,
.trainingOrderList li .orderDuration,
.trainingOrderList li .orderTime,
.trainingOrderList li .orderAddress {
    line-height: 22px;
    height: 22px;
    font-size: 14px;
    color: #666;
    margin-bottom: 6px;
}

.trainingOrderList li .orderType {
    padding-left: 20px;
    position: relative;
}

.trainingOrderList li .orderType::before {
    content: "📚";
    position: absolute;
    left: 0;
    top: 0;
    font-size: 14px;
}

.trainingOrderList li .orderDuration {
    padding-left: 20px;
    position: relative;
}

.trainingOrderList li .orderDuration::before {
    content: "⏰";
    position: absolute;
    left: 0;
    top: 0;
    font-size: 14px;
}

.trainingOrderList li .orderTime {
    padding-left: 20px;
    background: url(../image/new_zhdTime.png) no-repeat left center;
}

.trainingOrderList li .orderAddress {
    padding-left: 20px;
    background: url(../image/new_zhdAddress.png) no-repeat left center;
}

/* 订单底部 */
.trainingOrderList li .orderFooter {
    margin-top: 10px;
}

.trainingOrderList li .orderParticipants {
    height: 24px;
    line-height: 24px;
    padding: 0 12px;
    background: #f0f8ff;
    text-align: center;
    color: #0052d9;
    border-radius: 12px;
    font-size: 12px;
}

/* 原有的单个按钮样式（保留用于兼容） */
.trainingOrderList li .orderBtn:not(.institutionBtn):not(.trainingBtn) {
    height: 24px;
    border-radius: 24px;
    background: #fff;
    color: #0052d9;
    width: 80px;
    border: 1px solid #eaf2ff;
}

.trainingOrderList li .orderBtn:not(.institutionBtn):not(.trainingBtn) span {
    display: block;
    line-height: 24px;
    padding-left: 8px;
    background: url(../image/new_zhdArr.png) no-repeat 60px center;
    font-size: 12px;
}

.trainingOrderList li:hover .orderBtn:not(.institutionBtn):not(.trainingBtn) span {
    background: url(../../public/images/icons/icon_more.png) no-repeat 60px center;
}

.trainingOrderList li:hover .orderBtn:not(.institutionBtn):not(.trainingBtn) {
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
    color: #fff;
    animation: moreBtn3 0.5s 1 forwards;
    -webkit-animation: moreBtn3 0.5s 1 forwards;
    box-shadow: 0px 0px 10px 3px rgba(0, 77, 198, 0.35);
}

@keyframes moreBtn3 {
    from {
        background-position: 0;
    }

    to {
        background-position: 80px;
    }
}

/* 新的按钮组样式 */
.orderActions {
    display: flex;
    gap: 8px;
}

.orderActions .orderBtn {
    height: 28px;
    border-radius: 14px;
    border: none;
    cursor: pointer;
    font-size: 12px;
    padding: 0 12px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.orderActions .institutionBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 80px;
}

.orderActions .institutionBtn:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.orderActions .trainingBtn {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    width: 80px;
}

.orderActions .trainingBtn:hover {
    background: linear-gradient(135deg, #3da866 0%, #2f8a57 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
}

.orderActions .orderBtn span {
    display: block;
    line-height: 28px;
    padding: 0;
    background: none;
    font-weight: 500;
}

/* 按钮点击效果 */
.orderActions .orderBtn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

/* 新的页面布局样式 */
.main-content {
    display: flex;
    gap: 30px;
    margin-top: 30px;
}

.training-section {
    flex: 2;
    min-width: 0;
}

.sidebar {
    flex: 1;
    max-width: 350px;
    min-width: 300px;
    margin-top: 180px;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 25px;
    margin-top: 60px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.section-subtitle {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
    margin-bottom: 0;
}

.moreBtn {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white !important;
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
}

.moreBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

/* 无数据样式优化 */
.no-data-content {
    text-align: center;
    padding: 40px 20px;
}

.no-data-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.no-data-content p {
    color: #999;
    margin: 5px 0;
}

.no-data-tip {
    font-size: 12px;
    color: #ccc;
}

/* 侧边栏样式 */
.sidebar-widget {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    border: 1px solid #f0f0f0;
}

.widget-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
    position: relative;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 30px;
    height: 2px;
    background: #007bff;
}

/* 统计数据样式 */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px 10px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.stat-item:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #666;
}

/* 热门类型标签 */
.popular-types {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.type-tag {
    background: #e7f3ff;
    color: #007bff;
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.type-tag:hover {
    background: #007bff;
    color: white;
    transform: translateY(-1px);
}

/* 快速链接 */
.quick-links {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.quick-link {
    display: flex;
    align-items: center;
    padding: 10px 12px;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.quick-link:hover {
    background: #007bff;
    color: white;
    transform: translateX(5px);
}

.link-icon {
    margin-right: 10px;
    font-size: 16px;
}

.link-text {
    font-size: 14px;
}

/* 联系信息 */
.contact-info {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.contact-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
}

.contact-icon {
    margin-right: 10px;
    font-size: 16px;
    color: #007bff;
}

.contact-text {
    font-size: 14px;
    color: #666;
}

/* 侧边栏数据统计样式 */
.sidebar-widget .data-summary {
    margin-bottom: 15px;
}

.sidebar-widget .summary-item {
    margin-bottom: 8px;
}

.sidebar-widget .summary-text {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    display: block;
}

.sidebar-widget .update-time {
    font-size: 12px;
    color: #666;
    display: block;
}

.sidebar-widget .quick-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.sidebar-widget .action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 12px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    color: #495057;
    text-decoration: none;
    width: 100%;
    box-sizing: border-box;
}

.sidebar-widget .action-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.sidebar-widget .btn-icon {
    font-size: 14px;
}

.sidebar-widget .btn-text {
    font-size: 13px;
    font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .main-content {
        flex-direction: column;
    }

    .sidebar {
        max-width: none;
        min-width: auto;
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .bottomContent {
        flex-direction: column;
        height: auto;
        padding: 15px 20px;
        gap: 15px;
    }

    .bottomLeft, .bottomRight {
        flex: none;
    }

    .bottomStats {
        gap: 20px;
    }

    .bottomLinks {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
    }

    .trainingOrderList {
        grid-template-columns: 1fr;
    }

    .bottomBar {
        min-width: auto;
        height: auto;
    }

    .bottomStats {
        flex-direction: column;
        gap: 10px;
    }

    .statDivider {
        width: 80%;
        height: 1px;
    }

    .bottomLinks {
        flex-direction: column;
        width: 100%;
        gap: 10px;
    }

    .bottomLink {
        justify-content: center;
        width: 100%;
    }
}

/* 提示消息样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.toast-info {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

/* 确保内容不被底部遮挡 */
.main-content {
    margin-bottom: 20px;
}

/* 底部信息条样式 */
.bottomBar {
    width: 100%;
    min-width: 1400px;
    height: 120px;
    background: linear-gradient(135deg, #0052d9 0%, #097fcc 50%, #13aebe 100%);
    position: relative;
    margin-top: 30px;
}

.bottomContent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 120px;
    padding: 20px;
}

.bottomLeft {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 40px;
}

.bottomLogo {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logoText {
    font-size: 24px;
    font-weight: bold;
    color: #fff;
    line-height: 1;
}

.logoSubtext {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.bottomInfo {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.bottomInfo .info-text {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
    margin: 0;
}

.bottomRight {
    display: flex;
    gap: 60px;
    align-items: flex-start;
}

.contact-info, .service-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.contact-title, .service-title {
    font-size: 16px;
    font-weight: bold;
    color: #fff;
    margin: 0 0 5px 0;
}

.contact-item, .service-item {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.85);
    margin: 0;
    line-height: 1.3;
}

.bottomCenter {
    flex: 2;
    display: flex;
    justify-content: center;
}

.bottomStats {
    display: flex;
    align-items: center;
    gap: 30px;
}

.statItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.statNumber {
    font-size: 28px;
    font-weight: bold;
    color: #fff720;
    line-height: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.statLabel {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    margin-top: 4px;
}

.statDivider {
    width: 1px;
    height: 40px;
    background: rgba(255, 255, 255, 0.3);
}

.bottomRight {
    flex: 1;
    display: flex;
    justify-content: flex-end;
}

.bottomLinks {
    display: flex;
    gap: 15px;
}

.bottomLink {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: #fff;
    text-decoration: none;
    font-size: 13px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.bottomLink:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.linkIcon {
    font-size: 14px;
}

.linkText {
    font-size: 13px;
    font-weight: 500;
}

/* 培训订单详情页样式 */
.training-order-detail {
    background: #fff;
    border-radius: 8px;
    padding: 30px;
    margin: 30px 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.detail-header {
    border-bottom: 2px solid #eaf2ff;
    padding-bottom: 20px;
    margin-bottom: 30px;
}

.order-title {
    font-size: 28px;
    color: #333;
    margin-bottom: 15px;
    font-weight: bold;
}

.order-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.order-status {
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: bold;
    background: #52c41a;
    color: #fff;
}

.order-fee {
    font-size: 24px;
    font-weight: bold;
    color: #ff6b35;
}

.detail-section {
    margin-bottom: 30px;
}

.detail-section h3 {
    font-size: 20px;
    color: #333;
    margin-bottom: 15px;
    padding-left: 10px;
    border-left: 4px solid #0052d9;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
}

.info-item label {
    font-weight: bold;
    color: #666;
    min-width: 100px;
}

.order-description,
.order-requirements {
    line-height: 1.6;
    color: #666;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.detail-actions {
    text-align: center;
    margin-top: 40px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.btn-primary,
.btn-secondary {
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    margin: 0 10px;
    cursor: pointer;
    border: none;
}

.btn-primary {
    background: linear-gradient(to right, #0154d9 0%, #097fcc 50%, #13aebe 100%);
    color: #fff;
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

/* 培训订单列表页样式 */
.training-order-list-page {
    margin: 30px 0;
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
}

.page-title {
    font-size: 36px;
    color: #333;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 16px;
    color: #666;
}

.search-filters {
    background: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-item label {
    font-weight: bold;
    color: #333;
    white-space: nowrap;
}

.filter-item select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 120px;
}

.btn-search,
.btn-reset {
    padding: 8px 20px;
    border-radius: 4px;
    cursor: pointer;
    border: none;
    font-size: 14px;
}

.btn-search {
    background: #0052d9;
    color: #fff;
}

.btn-reset {
    background: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.order-list-container {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.order-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.order-card {
    border: 1px solid #eee;
    border-radius: 8px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.order-card:hover {
    box-shadow: 0 4px 12px rgba(0,82,217,0.2);
    border-color: #0052d9;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.order-card .order-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 0;
    flex: 1;
    margin-right: 10px;
}

.order-card .order-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    white-space: nowrap;
}

.status-published {
    background: #52c41a;
    color: #fff;
}

.status-ongoing {
    background: #faad14;
    color: #fff;
}

.status-completed {
    background: #1890ff;
    color: #fff;
}

.order-info p {
    margin: 8px 0;
    font-size: 14px;
    color: #666;
}

.order-info .label {
    font-weight: bold;
    color: #333;
}

.order-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #eee;
}

.participants {
    font-size: 14px;
    color: #0052d9;
    font-weight: bold;
}

.order-card .order-fee {
    font-size: 16px;
    font-weight: bold;
    color: #ff6b35;
}

.no-data {
    text-align: center;
    padding: 60px 0;
    color: #999;
    font-size: 16px;
}

.pagination-container {
    margin-top: 30px;
    text-align: center;
}

/* 列表头部样式 */
.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.list-stats {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.total-count {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.update-time {
    font-size: 12px;
    color: #666;
}

.list-actions {
    display: flex;
    gap: 10px;
}

.list-actions .action-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 13px;
    color: #495057;
    text-decoration: none;
}

.list-actions .action-btn:hover {
    background: #007bff;
    color: white;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.list-actions .btn-icon {
    font-size: 14px;
}

.list-actions .btn-text {
    font-size: 13px;
    font-weight: 500;
}

/* 加载状态样式 */
.loading-state {
    text-align: center;
    padding: 60px 20px;
}

.loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* 优化无数据样式 */
.no-data {
    text-align: center;
    padding: 80px 20px;
    color: #999;
    font-size: 16px;
}

.no-data-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.no-data-icon {
    font-size: 64px;
    opacity: 0.5;
}

.no-data-content p {
    margin: 5px 0;
    color: #999;
}

.no-data-tip {
    font-size: 14px;
    color: #ccc;
}

/* 提示消息样式 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.toast.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.toast-info {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .list-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .list-actions {
        justify-content: center;
        width: 100%;
    }

    .list-actions .action-btn {
        flex: 1;
        justify-content: center;
    }
}