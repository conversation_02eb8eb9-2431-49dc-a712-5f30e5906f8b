from PIL import Image, ImageDraw, ImageFont
import os

def create_placeholder_image(width, height, text, filename, bg_color=(240, 240, 240), text_color=(100, 100, 100)):
    """创建占位图片"""
    try:
        # 创建图片
        img = Image.new('RGB', (width, height), bg_color)
        draw = ImageDraw.Draw(img)
        
        # 尝试使用系统字体
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        # 计算文字位置
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        # 绘制文字
        draw.text((x, y), text, fill=text_color, font=font)
        
        # 保存图片
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        img.save(filename)
        print(f"创建占位图片: {filename}")
        return True
        
    except Exception as e:
        print(f"创建图片失败 {filename}: {e}")
        return False

def main():
    # 创建images目录
    os.makedirs("static_files/images", exist_ok=True)
    
    # 需要创建的占位图片
    images = [
        # 背景图片
        {'file': 'static_files/images/lb_bg.jpg', 'size': (1400, 276), 'text': '部门联办背景', 'bg': (70, 130, 180)},
        {'file': 'static_files/images/policySBanner.jpg', 'size': (1400, 260), 'text': '政策专区横幅', 'bg': (65, 105, 225)},
        
        # 图标文件 (正常状态)
        {'file': 'static_files/images/baseEnterIcon1.png', 'size': (60, 60), 'text': '创业补贴', 'bg': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon2.png', 'size': (60, 60), 'text': '残疾人补贴', 'bg': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon3.png', 'size': (60, 60), 'text': '担保贷款', 'bg': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon4.png', 'size': (60, 60), 'text': '融资贷款', 'bg': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon5.png', 'size': (60, 60), 'text': '退役军人', 'bg': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon6.png', 'size': (60, 60), 'text': '企业注册', 'bg': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon7.png', 'size': (60, 60), 'text': '一件事', 'bg': (255, 255, 255)},
        
        # 图标文件 (激活状态)
        {'file': 'static_files/images/baseEnterIcon1on.png', 'size': (60, 60), 'text': '创业补贴', 'bg': (0, 82, 217), 'text_color': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon2on.png', 'size': (60, 60), 'text': '残疾人补贴', 'bg': (0, 82, 217), 'text_color': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon3on.png', 'size': (60, 60), 'text': '担保贷款', 'bg': (0, 82, 217), 'text_color': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon4on.png', 'size': (60, 60), 'text': '融资贷款', 'bg': (0, 82, 217), 'text_color': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon5on.png', 'size': (60, 60), 'text': '退役军人', 'bg': (0, 82, 217), 'text_color': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon6on.png', 'size': (60, 60), 'text': '企业注册', 'bg': (0, 82, 217), 'text_color': (255, 255, 255)},
        {'file': 'static_files/images/baseEnterIcon7on.png', 'size': (60, 60), 'text': '一件事', 'bg': (0, 82, 217), 'text_color': (255, 255, 255)},
        
        # 其他图片
        {'file': 'static_files/images/baseEnterOn.png', 'size': (264, 212), 'text': '激活背景', 'bg': (0, 82, 217)},
        {'file': 'static_files/images/tips.png', 'size': (49, 50), 'text': '?', 'bg': (255, 165, 0)},
        {'file': 'static_files/images/tipson.png', 'size': (49, 50), 'text': '?', 'bg': (255, 140, 0)},
        {'file': 'static_files/images/logo.png', 'size': (120, 50), 'text': '青创通', 'bg': (255, 255, 255)},
    ]
    
    # 创建所有占位图片
    success_count = 0
    for img_info in images:
        text_color = img_info.get('text_color', (100, 100, 100))
        if create_placeholder_image(
            img_info['size'][0], 
            img_info['size'][1], 
            img_info['text'], 
            img_info['file'],
            img_info['bg'],
            text_color
        ):
            success_count += 1
    
    print(f"\n占位图片创建完成！成功创建 {success_count}/{len(images)} 个图片文件")

if __name__ == "__main__":
    main()
