  /*background: url(../../images/ZctImages/Mplay.png) no-repeat;*/
  
@charset "utf-8";
/**
 * SMusic
 * Author:S<PERSON>han
 * Version:2.0.0
 * url: http://www.smohan.net/lab/smusic.html
 * 使用请保留以上信息
 */
/*reset*/
/*html,body,h1,h2,h3,h4,h5,h6,div,dl,dt,dd,ul,ol,li,p,blockquote,pre,hr,figure,table,caption,th,td,form,fieldset,legend,input,button,textarea,menu{margin:0;padding:0;-webkit-tap-highlight-color:rgba(0,0,0,0);word-wrap: break-word;font-size: inherit;line-height: inherit;overflow: visible;}
header,footer,section,article,aside,nav,address,figure,figcaption,menu,details{display:block;}*/
.f-cb{height: 0;}
.f-cb:after{display:block;content:" ";height:0;visibility:hidden;clear: both;}
.f-ib{display:inline-block;}
.f-din{display:inline;}
.f-dn{display:none;}
.f-db{display:block;}
.f-fl{float:left;}
.f-fr{float:right;}
.f-fwn{font-weight:normal;}
.f-fwb{font-weight:bold;}
.f-tal{text-align:left;}
.f-tac{text-align:center;}
.f-tar{text-align:right;}
.f-oh{overflow: hidden;zoom: 1;clear: both;}
.f-tdn{text-decoration: none!important;}
.f-vam,.f-vama *{vertical-align:middle;}
.f-wsn{word-wrap:normal;white-space:nowrap;}
.f-pre{overflow:hidden;text-align:left;white-space:pre-wrap;word-wrap:break-word;word-break:break-all;}
.f-wwb{white-space:normal;word-wrap:break-word;word-break:break-all;}
.f-ti{overflow:hidden;text-indent:-30000px;}
.f-lhn{line-height:normal;}
.f-toe{overflow:hidden;word-wrap:normal!important;white-space:nowrap;text-overflow:ellipsis;}
.f-usn{-webkit-user-select:none;user-select:none;}
.f-bsb{-webkit-box-sizing:border-box;box-sizing:border-box;}
.f-cp{cursor: pointer}

/*滚动条美化*/
/*::-webkit-scrollbar{width:6px;height:6px}
::-webkit-scrollbar-button:vertical{display:none}
::-webkit-scrollbar-track:vertical{background-color:transparent;}
::-webkit-scrollbar-track-piece{background-color:transparent;}
::-webkit-scrollbar-thumb:vertical{background-color:rgba(55,146,239,.3);border-radius:6px}
::-webkit-scrollbar-thumb:vertical:hover,
::-webkit-scrollbar-thumb:vertical:active {background-color: #2e86ef}*/
/*SMusic*/
.grid-music-container{
    width: 320px;
    height: 166px;
    position: relative;
}

.grid-music-container .m-music-play-wrap{
    height: 150px;
    position: relative;
}
.u-music-title{
	height: 45px;
	margin-left: 90px;
}
.grid-music-container .u-cover{
    width: 72px;
    height: 72px;
    border-radius: 50%;
    overflow: hidden;
}
.grid-music-container .u-cover img{
    display: block;
    width: 100%;
   	height: 100%;
}
.grid-music-container .u-cover.play{
    -webkit-animation: Circle 10s linear infinite 0s forwards;
    animation: Circle 10s linear infinite 0s forwards;
}
.grid-music-container .u-cover.paused{
    -webkit-animation-play-state: paused;
    animation-play-state: paused;
}
@-webkit-keyframes Circle {
    from{
        -webkit-transform: rotate(0deg);
    }
    to{
        -webkit-transform: rotate(360deg);
    }
}
@keyframes Circle {
    from{
        transform: rotate(0deg);
    }
    to{
        transform: rotate(360deg);
    }
}
.grid-music-container .m-now-info{
    height: 100%;
    padding: 15px 5px 0 15px;
}
.grid-music-container .m-now-info .playerTitle{
		width: 200px; 
		height: 42px;
		line-height: 20px;
}
.grid-music-container .m-now-controls{
    position: relative;
}
.grid-music-container .m-now-controls .u-control{
    display: inline-block;
    vertical-align:middle;
    font-size: 0;
    overflow: hidden;
}
.u-control.u-volume{
	margin-top: 3px;
	padding-left: 5px;
}
.grid-music-container .m-now-controls .u-process{
    width: 155px;
    height: 5px;
    margin-left: 10px;
    position: relative;
    background-color: #cecfd4;
    cursor: pointer;
}
.grid-music-container .m-now-controls .u-process .buffer-process,.grid-music-container .m-now-controls .u-process .current-process{
    display: block;
    width: 0;
    height: 4px;
    position: absolute;
    top:0;
    left: 0;
    background-color: #3792ef;
    z-index: 1;
}
.grid-music-container .m-now-controls .u-process .buffer-process{
    z-index: 0;
    background-color: #c1c2c0;
}
.grid-music-container .m-now-controls .u-time{
    margin-left: 10px;
    font-size: 12px;
    color: #fff;
    width: 80px;
}
.grid-music-container .m-now-controls .u-volume{
    overflow: visible;
    text-align: center;
    position: relative;
    margin-left: 20px;
}
.grid-music-container .u-volume .volume-process{
    width: 3px;
    height: 40px;
    background: #cecfd4;
    position: absolute;
    top: 54px;
    right: 25px;
    cursor: pointer;
    visibility: hidden; /*设置不可见性，最好不要使用display:none，不然高度很难获取*/
}
.grid-music-container .u-volume .volume-process.show{
    visibility: visible;
}
.grid-music-container .u-volume .volume-process .volume-current,
.grid-music-container .u-volume .volume-process .volume-event{
    display: inline-block;
    width: 3px;
    height: 50%;
    background-color: #3792ef;
    position: absolute;
    left: 0;
    bottom:0;
    -webkit-transition: height .2s linear;
    transition: height .2s linear;
}
.grid-music-container .u-volume .volume-process .volume-event{
    width: 21px;
    left: -10px;
    background: none;
    height: 100%;
    z-index: 1;
}
.grid-music-container .u-volume .volume-process .volume-bar{
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 100%;
    background-color: #fff;
    border: 1px solid #a8a9a7;
    position: absolute;
    left: -3px;
    bottom: 50%;
    -webkit-transition: bottom .2s linear;
    transition: bottom .2s linear;
}
.grid-music-container .u-volume .volume-process .volume-bar:hover,
.grid-music-container .u-volume .volume-process .volume-bar:active{
    background-color: #f1f2f0;
}
.grid-music-container .u-volume .volume-control{
    display: inline-block;
    width: 18px;
    height: 18px;
    background: url(../images/volume.png) no-repeat center center;
    /*background: url(../../../Content/ZCT/V2/images/Delete/data/fyc.jpg) no-repeat center center;
    background: url(../../../Content/ZCT/V2/images/Delete/data/wanghouyusheng.mp3) no-repeat center center;*/
    cursor: pointer;
}
.grid-music-container .u-volume .volume-control:hover{
    opacity: 0.8;
}
.grid-music-container .m-music-list-wrap{
    margin-left: 135px;
    margin-right: 25px;
    height: 120px;
    border: 1px solid rgba(255,255,255,.7);
    overflow-x: hidden;
    overflow-y: auto;
}
.grid-music-container .m-music-list-wrap ul{}
.grid-music-container .m-music-list-wrap li{
    display: block;
    line-height: 30px;
    padding: 0 10px;
    cursor: pointer;
    color: #383937;
    font-size: 14px;
}
.grid-music-container .m-music-list-wrap li strong{
    font-size: 16px;
    font-weight: normal;
}
.grid-music-container .m-music-list-wrap li:hover,.grid-music-container .m-music-list-wrap li.current{
    background-color: rgba(255,255,255,.7);
    color: #3792ef;
}
.grid-music-container .m-music-list-wrap li.current{
    background-color: transparent;
}

.m-now-info .play_buttons{
		width: 300px; 
		margin-top: 40px;
}
.grid-music-container .m-play-controls{
   width: 100px;
}
.grid-music-container .m-play-controls a{
    display: inline-block;
    vertical-align: middle;
}
.grid-music-container .m-play-controls .u-play-btn{
    display: inline-block;
    /*width: 30px;*/
    height: 30px;
    cursor: pointer;
}
.grid-music-container .m-play-controls .u-play-btn:hover{
	opacity: 0.8;
}
.u-play-btn.prev,.u-play-btn.next{
		 width: 30px;
     background: url(../images/next.png) no-repeat center center;
}
.u-play-btn.prev{
	background: url(../images/prev.png) no-repeat center center;
}
.u-play-btn.play{
	width: 30px;
	background: url(../images/pause.png) no-repeat center center;
}
.u-play-btn.paused{
	width: 30px;
	background: url(../images/Mplay.png) no-repeat center center;
}
.u-play-btn.mode{
    height: 0px!important;
}
.u-play-btn.mode-list{
    margin-left: 65px;
}
.u-play-btn.mode-list.current{
    background-position: -221px -98px;
}
.u-play-btn.mode-random{
    background-position: -201px -80px;
}
.u-play-btn.mode-random.current{
    background-position: -241px -80px;
}
.u-play-btn.mode-single{
    background-position: -181px -80px;
}
.u-play-btn.mode-single.current{
    background-position: -221px -80px;
}