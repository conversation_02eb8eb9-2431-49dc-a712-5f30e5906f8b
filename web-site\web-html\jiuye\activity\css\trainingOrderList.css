/* 培训订单列表页面样式 */

/* 页面基础样式 */
.training-order-list-page-new {
    max-width: none;
    width: 100%;
    margin: 0;
    padding: 20px 0.5%;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 覆盖外层容器样式，让页面占用更多宽度 */
.conAuto3 {
    width: 96% !important;
    max-width: none !important;
    min-width: 1200px !important;
    margin-left: auto !important;
    margin-right: auto !important;
    padding: 0 2% !important;
    box-sizing: border-box !important;
}

/* 强制覆盖可能的布局限制 */
.main-layout-container {
    max-width: none !important;
    width: 100% !important;
}

/* 确保页面背景容器也足够宽 */
.pageBg {
    width: 100% !important;
}

.bgBox {
    min-width: 100% !important;
}

/* 页面标题区域 */
.page-header-new {
    margin-bottom: 30px;
    padding: 30px 0;
    border-bottom: 1px solid #e8e8e8;
}

.header-content-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.title-section-new {
    flex: 1;
}

.page-title-new {
    font-size: 32px;
    font-weight: 700;
    color: #1a1a1a;
    margin: 0 0 10px 0;
    line-height: 1.2;
}

.title-meta-new {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.meta-tag-new {
    font-size: 20px;
}

.subtitle-text {
    color: #666;
    font-size: 16px;
    font-weight: 400;
}

.header-actions-new {
    display: flex;
    gap: 12px;
}

.action-btn-new {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
}

.action-btn-new.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.action-btn-new.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.action-btn-new.secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.action-btn-new.secondary:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.btn-icon-new {
    font-size: 16px;
}

.btn-text-new {
    font-size: 14px;
}

/* 搜索筛选区域 */
.search-filters-new {
    margin-bottom: 30px;
}

.filter-container-new {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e9ecef;
}

.filter-title-new {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
}

.filter-icon-new {
    font-size: 18px;
}

.filter-row-new {
    display: flex;
    align-items: end;
    gap: 20px;
    flex-wrap: wrap;
}

.filter-item-new {
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-width: 160px;
}

.filter-label-new {
    font-size: 14px;
    font-weight: 500;
    color: #495057;
}

.filter-select-new {
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.filter-select-new:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions-new {
    display: flex;
    gap: 12px;
    align-items: end;
}

.btn-search-new, .btn-reset-new {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-search-new {
    background: #667eea;
    color: white;
}

.btn-search-new:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-reset-new {
    background: #6c757d;
    color: white;
}

.btn-reset-new:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* 统计信息区域 */
.stats-section-new {
    margin-bottom: 30px;
}

.stats-container-new {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.stat-item-new {
    display: flex;
    align-items: center;
    gap: 12px;
}

.stat-icon-new {
    font-size: 24px;
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content-new {
    display: flex;
    flex-direction: column;
}

.stat-number-new {
    font-size: 24px;
    font-weight: 700;
    color: #495057;
    line-height: 1;
}

.stat-label-new {
    font-size: 14px;
    color: #6c757d;
    margin-top: 4px;
}

.stat-divider-new {
    width: 1px;
    height: 40px;
    background: #dee2e6;
}

/* 订单列表区域 */
.order-list-container-new {
    position: relative;
}

.order-grid-new {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2%;
    margin-bottom: 30px;
    padding: 0;
    width: 100%;
}

/* 订单卡片样式 */
.order-card-new {
    position: relative;
    background: white;
    border-radius: 10px;
    border: 1px solid #e9ecef;
    overflow: hidden;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    height: fit-content;
    min-height: 320px;
    width: 100%;
    min-width: 280px;
}

.order-card-new:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    border-color: #667eea;
}

.card-status-new {
    position: absolute;
    top: 16px;
    right: 16px;
    z-index: 2;
}

.status-text-new {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    background: #28a745;
    color: white;
}

.status-draft .status-text-new {
    background: #6c757d;
}

.status-published .status-text-new {
    background: #28a745;
}

.status-ongoing .status-text-new {
    background: #ffc107;
    color: #212529;
}

.status-completed .status-text-new {
    background: #17a2b8;
}

.status-cancelled .status-text-new {
    background: #dc3545;
}

.card-header-new {
    padding: 16px 16px 12px 16px;
    border-bottom: 1px solid #f1f3f4;
}

.order-title-new {
    font-size: 16px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0 0 10px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 42px;
}

.order-meta-tags-new {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.type-tag-new {
    background: #e3f2fd;
    color: #1976d2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.level-tag-new {
    background: #f3e5f5;
    color: #7b1fa2;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.card-content-new {
    padding: 16px;
}

.info-grid-new {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin-bottom: 16px;
}

.info-item-new {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.info-icon-new {
    font-size: 14px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    border-radius: 6px;
    flex-shrink: 0;
}

.info-content-new {
    flex: 1;
    min-width: 0;
}

.info-label-new {
    font-size: 11px;
    color: #6c757d;
    margin-bottom: 2px;
    font-weight: 500;
}

.info-value-new {
    font-size: 13px;
    color: #495057;
    font-weight: 500;
    word-break: break-word;
    line-height: 1.3;
}

.price-highlight-new {
    color: #e74c3c !important;
    font-weight: 600 !important;
}

/* 进度条样式 */
.progress-section-new {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #f1f3f4;
}

.progress-header-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.progress-label-new {
    font-size: 12px;
    font-weight: 500;
    color: #495057;
}

.progress-stats-new {
    font-size: 12px;
    color: #6c757d;
}

.progress-bar-container-new {
    display: flex;
    align-items: center;
    gap: 8px;
}

.progress-bar-bg-new {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
}

.progress-bar-fill-new {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-percentage-new {
    font-size: 11px;
    font-weight: 600;
    color: #495057;
    min-width: 30px;
    text-align: right;
}

/* 卡片底部操作区域 */
.card-footer-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.btn-view-detail-new {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 5px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-view-detail-new:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* 新的按钮组样式 */
.card-actions-new {
    display: flex;
    gap: 8px;
}

.btn-institution-new,
.btn-signup-new {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-institution-new {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-institution-new:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-signup-new {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.btn-signup-new:hover {
    background: linear-gradient(135deg, #3da866 0%, #2f8a57 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
}

.btn-institution-new .btn-icon-new,
.btn-signup-new .btn-icon-new {
    font-size: 12px;
}

.btn-institution-new .btn-text-new,
.btn-signup-new .btn-text-new {
    font-size: 12px;
}

.card-price-new {
    font-size: 16px;
    font-weight: 700;
    color: #e74c3c;
}

/* 无数据状态 */
.no-data-new {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 2px dashed #dee2e6;
}

.no-data-content-new {
    text-align: center;
    max-width: 400px;
    padding: 40px 20px;
}

.no-data-icon-new {
    font-size: 64px;
    margin-bottom: 20px;
    opacity: 0.5;
}

.no-data-text-new h3 {
    font-size: 20px;
    color: #495057;
    margin: 0 0 8px 0;
    font-weight: 600;
}

.no-data-text-new p {
    font-size: 14px;
    color: #6c757d;
    margin: 0 0 24px 0;
    line-height: 1.5;
}

.retry-btn-new {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 auto;
}

.retry-btn-new:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

/* 加载状态 */
.loading-state-new {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background: #f8f9fa;
    border-radius: 12px;
}

.loading-content-new {
    text-align: center;
    padding: 40px 20px;
}

.loading-spinner-new {
    width: 40px;
    height: 40px;
    border: 4px solid #e9ecef;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text-new {
    font-size: 16px;
    color: #6c757d;
    margin: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .order-grid-new {
        grid-template-columns: repeat(3, 1fr);
        gap: 2.5%;
    }

    .conAuto3 {
        width: 95% !important;
        padding: 0 2.5% !important;
    }
}

@media (max-width: 900px) {
    .order-grid-new {
        grid-template-columns: repeat(2, 1fr);
        gap: 3%;
    }

    .conAuto3 {
        width: 94% !important;
        padding: 0 3% !important;
    }
}

@media (max-width: 768px) {
    .training-order-list-page-new {
        padding: 16px;
        margin: 0 16px;
    }

    .header-content-new {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }

    .page-title-new {
        font-size: 24px;
    }

    .header-actions-new {
        justify-content: center;
    }

    .filter-row-new {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-item-new {
        min-width: auto;
    }

    .filter-actions-new {
        justify-content: center;
        margin-top: 16px;
    }

    .stats-container-new {
        flex-direction: column;
        gap: 16px;
        text-align: center;
    }

    .stat-divider-new {
        width: 100%;
        height: 1px;
    }

    .order-grid-new {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .info-grid-new {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .card-footer-new {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }

    .card-actions-new {
        justify-content: center;
        gap: 6px;
    }

    .btn-institution-new,
    .btn-signup-new {
        flex: 1;
        justify-content: center;
        min-width: 0;
    }

    .btn-view-detail-new {
        justify-content: center;
    }

    .card-price-new {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .training-order-list-page-new {
        padding: 12px;
        margin: 0 8px;
    }

    .page-title-new {
        font-size: 20px;
    }

    .filter-container-new {
        padding: 16px;
    }

    .order-card-new {
        border-radius: 12px;
    }

    .card-header-new,
    .card-content-new {
        padding: 16px;
    }

    .card-footer-new {
        padding: 12px 16px;
    }
}

/* 动画效果 */
.order-card-new {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
