//  var orgUrl = 'http://123.232.102.29:18800/'; // 试运行使用
// var orgUrl = 'http://123.232.102.30:20915/'; // 开发、测试
var orgUrl_finance = 'https://hrss.qingdao.gov.cn/bank/'; 
 var orgUrl = 'https://hrss.qingdao.gov.cn/'; // 试运行域名
// var orgUrl = 'https://123.232.102.29:18803/'; // 演示

var fileUrl = 'https://file.zhenghe.cn/'//开发-上传附件地址
var fileToken = 'iAmRtlarUMenj2tKcPI5x4tYwWuUqLi8VM1qzzEz+cQ='//开发-文件服务器token
jQuery.support.cors = true;
var pageIndex = 0; //页面索引初始值
var pageSize = 12; //每页显示条数初始化，修改显示条数，修改这里即可
var token = localStorage.getItem("token")
var PostData = ''
var getData = ''
var deleteData = ''
var putData = ''
// var Base64 = function () {
//     var base64hash = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

//     // btoa method
//     function _btoa(s) {
//         if (/([^\u0000-\u00ff])/.test(s)) {
//             throw new Error('INVALID_CHARACTER_ERR');
//         }
//         var i = 0,
//             prev,
//             ascii,
//             mod,
//             result = [];

//         while (i < s.length) {
//             ascii = s.charCodeAt(i);
//             mod = i % 3;`k'k'j'mu`

//             switch (mod) {
//                 // 第一个6位只需要让8位二进制右移两位
//                 case 0:
//                     result.push(base64hash.charAt(ascii >> 2));
//                     break;
//                 //第二个6位 = 第一个8位的后两位 + 第二个8位的前4位
//                 case 1:
//                     result.push(base64hash.charAt((prev & 3) << 4 | (ascii >> 4)));
//                     break;
//                 //第三个6位 = 第二个8位的后4位 + 第三个8位的前2位
//                 //第4个6位 = 第三个8位的后6位
//                 case 2:
//                     result.push(base64hash.charAt((prev & 0x0f) << 2 | (ascii >> 6)));
//                     result.push(base64hash.charAt(ascii & 0x3f));
//                     break;
//             }

//             prev = ascii;
//             i++;
//         }

//         // 循环结束后看mod, 为0 证明需补3个6位，第一个为最后一个8位的最后两位后面补4个0。另外两个6位对应的是异常的“=”；
//         // mod为1，证明还需补两个6位，一个是最后一个8位的后4位补两个0，另一个对应异常的“=”
//         if (mod == 0) {
//             result.push(base64hash.charAt((prev & 3) << 4));
//             result.push('==');
//         } else if (mod == 1) {
//             result.push(base64hash.charAt((prev & 0x0f) << 2));
//             result.push('=');
//         }

//         return result.join('');
//     }

//     // atob method
//     // 逆转encode的思路即可
//     function _atob(s) {
//         s = s.replace(/\s|=/g, '');
//         var cur,
//             prev,
//             mod,
//             i = 0,
//             result = [];

//         while (i < s.length) {
//             cur = base64hash.indexOf(s.charAt(i));
//             mod = i % 4;

//             switch (mod) {
//                 case 0:
//                     //TODO
//                     break;
//                 case 1:
//                     result.push(String.fromCharCode(prev << 2 | cur >> 4));
//                     break;
//                 case 2:
//                     result.push(String.fromCharCode((prev & 0x0f) << 4 | cur >> 2));
//                     break;
//                 case 3:
//                     result.push(String.fromCharCode((prev & 3) << 6 | cur));
//                     break;

//             }

//             prev = cur;
//             i++;
//         }

//         return result.join('');
//     }

//     return {
//         btoa: _btoa,
//         atob: _atob,
//         encode: _btoa,
//         decode: _atob
//     };
// }();

// params = JSON.stringify(obj);
/**
 * 基础ajax（post）方式 异步 */
function ajaxPostData(urls, param, callback) {
    $.ajax({
        async: true,
        type: "post",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        xhrFields: {withCredentials: true},
        contentType: "application/json",
        success: function (data) {
            callback(data);
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 基础ajax（post）方式 同步*/
function ajaxPostDataFull(urls, param) {
    $.ajax({
        async: false,
        type: "post",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        contentType: "application/json",
        success: function (data) {
            PostData = data;
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 基础ajax（get）方式 异步*/
function ajaxgetData(urls, param, callback) {
    $.ajax({
        async: true,
        type: "get",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        contentType: "application/json",
        success: function (data) {
            callback(data);
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 基础ajax（get）方式 同步*/
function ajaxgetDataFull(urls, param) {
    $.ajax({
        async: false,
        type: "get",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        success: function (data) {
            getData = data;
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 带token
 * 基础ajax（post）方式 异步 */
function ajaxPostData_token(urls, param, callback) {
    $.ajax({
        async: true,
        type: "post",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        xhrFields: {withCredentials: true},
        contentType: "application/json",
        beforeSend: function (request) {
            if (token) {
                request.setRequestHeader("Authorization", 'Bearer ' + token);
            } else {
                request.setRequestHeader("Authorization", 'Basic ' + window.btoa('webapp:123456'));
            }
        },
        success: function (data) {
            callback(data);
            if (data.code == 401) {
                localStorage.removeItem("token")
            }
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 带token
 * 基础ajax（post）方式 同步*/
function ajaxPostDataFull_token(urls, param) {
    $.ajax({
        async: false,
        type: "post",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        contentType: "application/json",
        beforeSend: function (request) {
            if (token) {
                request.setRequestHeader("Authorization", 'Bearer ' + token);
            } else {
                request.setRequestHeader("Authorization", 'Basic ' + window.btoa('webapp:123456'));
            }
        },
        success: function (data) {
            PostData = data;
            if (data.code == 401) {
                localStorage.removeItem("token")
            }
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};
/**
 * 带token
 * 基础ajax（post）方式 同步 导入*/
function ajaxPostDataFull_import(urls, param) {
    $.ajax({
        async: false,
        url: orgUrl + urls,
        type: "POST",
        cache: false,
        data: param,
        processData: false,
        contentType: false,
        dataType: "json",
        xhrFields: {
            withCredentials: true
        },
        beforeSend: function (request) {
            if (token) {
                request.setRequestHeader("Authorization", 'Bearer ' + token);
            } else {
                request.setRequestHeader("Authorization", 'Basic ' + window.btoa('webapp:123456'));
            }
        },
        success: function (data) {
            PostData = data;
            if (data.code == 401) {
                localStorage.removeItem("token")
            }
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};
/**
 * 带token
 * 基础ajax（get）方式 异步*/
function ajaxgetData_token(urls, param, callback) {
    $.ajax({
        async: true,
        type: "get",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        contentType: "application/json",
        beforeSend: function (request) {
            if (token) {
                request.setRequestHeader("Authorization", 'Bearer ' + token);
            } else {
                request.setRequestHeader("Authorization", 'Basic ' + window.btoa('webapp:123456'));
            }
        },
        success: function (data) {
            callback(data);
            if (data.code == 401) {
                localStorage.removeItem("token")
            }
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 带token ,文档流下载
 * 基础ajax（get）方式 异步*/
function ajaxgetData_tokenBlod(urls, param, callback) {
    $.ajax({
        type: "get",
        url: orgUrl + urls,
        data: param,
        xhrFields: {
            responseType: "arraybuffer",
        },
        beforeSend: function (request) {
            if (token) {
                request.setRequestHeader("Authorization", 'Bearer ' + token);
            } else {
                request.setRequestHeader("Authorization", 'Basic ' + window.btoa('webapp:123456'));
            }
        },

        success: function (data) {
            callback(data);
            if (data.code == 401) {
                localStorage.removeItem("token")
            }
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 带token
 * 基础ajax（get）方式 同步*/
function ajaxgetDataFull_token(urls, param) {
    $.ajax({
        async: false,
        type: "get",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        beforeSend: function (request) {
            if (token) {
                request.setRequestHeader("Authorization", 'Bearer ' + token);
            } else {
                request.setRequestHeader("Authorization", 'Basic ' + window.btoa('webapp:123456'));
            }
        },
        success: function (data) {
            getData = data;
            if (data.code == 401) {
                localStorage.removeItem("token")
            }
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 带token
 * 基础ajax（delete）方式 同步 */
function ajaxdeleteDataFull(urls, param) {
    $.ajax({
        async: false,
        type: "delete",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        xhrFields: {
            withCredentials: true
        },
        beforeSend: function (request) {
            if (token) {
                request.setRequestHeader("Authorization", 'Bearer ' + token);
            } else {
                request.setRequestHeader("Authorization", 'Basic ' + window.btoa('app:123456'));
            }
        },
        success: function (data) {
            deleteData = data;
            if (data.code == 401) {
                localStorage.removeItem("token")
            }
        },
        error: function (data) {
        }
    });
};

/**
 * 基础ajax（put）方式 同步*/
// function ajaxPutDataFull(urls, param) {
//     $.ajax({
//         async: false,
//         type: "put",
//         url: orgUrl + urls,
//         data: param,
//         dataType: "JSON",
//         contentType: "application/json",
//         beforeSend: function (request) {
//             if (token) {
//                 request.setRequestHeader("Authorization", 'Bearer ' + token);
//             } else {
//                 request.setRequestHeader("Authorization", 'Basic ' + window.btoa('webapp:123456'));
//             }
//         },
//         success: function (data) {
//             putData = data;
//             if (data.code == 401) {
//                 localStorage.removeItem("token")
//             }
//         },
//         error: function (data) {
//         }
//     });
// };
// h5-政策计算器
/**
 * 基础ajax（post）方式 同步*/
function ajaxPostDataFull_h5(urls, param) {
    $.ajax({
        async: false,
        type: "post",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        success: function (data) {
            PostData = data;
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};
/**
 * 基础ajax（get）方式 异步*/
function ajaxgetData_h5(urls, param,callback) {
    $.ajax({
        async: true,
        type: "get",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        contentType: "application/json",
        success: function (data) {
            callback(data);
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};
/**
 * 基础ajax（get）方式 同步*/
function ajaxgetDataFull_h5(urls, param) {
    $.ajax({
        async: false,
        type: "get",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        success: function (data) {
            getData = data;
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};

/**
 * 基础ajax（put）方式 同步*/
// function ajaxPutDataFull_h5(urls, param) {
//     $.ajax({
//         async: false,
//         type: "put",
//         url: orgUrl + urls,
//         data: param,
//         dataType: "JSON",
//         contentType: "application/json",
//         success: function (data) {
//             putData = data;
//         },
//         error: function (data) {}
//     });
// };
// h5-政策专区
/**
 * 基础ajax（post）方式 同步*/
 function ajaxPostData_h5(urls, param) {
    $.ajax({
        async: false,
        type: "post",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        xhrFields: {withCredentials: true},
        contentType: "application/json",
        success: function (data) {
            PostData = data;
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};
/**
 * 基础ajax（post）方式 异步*/
function ajaxPostData_token_h5(urls, param, callback) {
    $.ajax({
        async: true,
        type: "post",
        url: orgUrl + urls,
        data: param,
        dataType: "JSON",
        xhrFields: {withCredentials: true},
        contentType: "application/json",
        success: function (data) {
            callback(data);
        },
        error: function (data) {
            window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};




// ///////////////////////////////////////////////////////////////////////  金融第三方接口ajax    ///////////////////////////////////////////////////////////////
/**
 * 基础ajax（post）方式 异步 */
function ajaxPostData_financial(urls, param, callback) {
    $.ajax({
        async: true,
        type: "post",
        url: orgUrl_finance + urls,
        data: param,
        dataType: "JSON",
        xhrFields: {withCredentials: true},
        contentType: "application/json",
        success: function (data) {
            callback(data);
        },
        error: function (data) {
            // window.location.href='https://hrss.qingdao.gov.cn/cy/default.html'
        }
    });
};