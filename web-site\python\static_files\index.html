<!DOCTYPE html>

<html>



<head>

    <meta name="keywords" content="青创通 · 西宁市创业服务云平台">

    <meta name="description">

    <meta charset="UTF-8">

    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE11">

    <meta http-equiv="Content-Type"

        content="text/html; charset=UTF-8; X-Content-Type-Options=nosniff; X-XSS-Protection: 1;mode=block" />

    <title>找政策-青创通 · 西宁市创业服务云平台</title>

    <!-- base css -->

    <link rel="stylesheet" type="text/css" href="css/zh.min.css" />

    <!-- jbox css -->

    <link rel="stylesheet" type="text/css" href="css/jbox.css" />

    <!--分页 css-->

    <link rel="stylesheet" type="text/css" href="css/pagination.css" />

    <!-- common css -->

    <link rel="stylesheet" type="text/css" href="css/common.css" />

    <!-- this page css -->

    <link rel="stylesheet" type="text/css" href="css/policySpecial.css" />

    <link rel="shortcut icon" href="../public/images/icons/favicon.ico" type="image/x-icon" />

</head>



<body id="viewModelBox">

    <a href="https://zccx.qingdao.gov.cn/pcSite/index.html" target="_blank" class="qdzct"></a>

    <!-- 办事指南 start -->

    <div class="alertBg3 ">

        <div class="alertCont">

            <div class="alertTitle">

                办事指南 

                <div class="alertClose"><img src="images/closeIcon.png" ></div>

            </div>

            <div class="alertUl">

                <ul>

                    <li>

                        <p>个人申请创业担保贷款资格审核</p>

                        <a target="_blank" href="http://qdzwfw.sd.gov.cn/qd/icity/proinfo/indexggfw?code=f1314e94-5998-4b4d-af8f-4c0ef7ea8b86">线上资格审核</a>

                    </li>

                    <li>

                        <p>小微企业申请创业担保贷款资格审核</p>

                        <a target="_blank" href="http://qdzwfw.sd.gov.cn/qd/icity/proinfo/indexggfw?code=ffcf74be-7042-4f70-b384-ca446c4fc1db">线上资格审核</a>

                    </li>

                </ul>

            </div>

        </div>

    </div>

    <!-- 办事指南 end -->

    <!-- 残疾人 start -->

    <div class="alertBg1 cjrpop">

        <div class="alertCont">

            <div class="alertTitle">

                残疾人—次性创业补贴范围和条件 

                <div class="alertClose"><img src="images/closeIcon.png" ></div>

            </div>

            <div class="alertUl">

                <div class="infos">

                    1. 2023年1月1日及以后,具有本市户籍、在法定劳动年龄内、有劳动能力和就业创业愿望、持有 《中华人民共和国残疾人证》、登记失业的残疾人,本市行政区域内首次创办个体工商户、小微企业、民办非企业单位、社会团体、事务所等创业实体 (不含通过变更法定代表人或负责人方式创业的创业实体),可按规定申领一次性创业补贴。首次创办,指创业者在本市行政区域内首次登记注册创业实体。</br>

                    2. 申领范围内的创业者创办的创业实体,须取得营业执照等有效资质1年以上,且在申领补贴时创业实体处于正常经营状态。</br>

                    3. 申领一次性创业补贴时,创业者本人须在创业实体办理就业登记,缴纳职工社会保险12个月及以上。其中,无雇工的个体工商户创业者,可创办创业实体后在我市以灵活就业人员身份缴纳企业职工基本养老保险12个月及以上。</br>

                    4. 在补贴申领及审核过程中,创业者本人应处于职工社会保险费在缴状态。</br>

                    5. 申领一次性创业补贴人员,须为创业实体的法定代表人或负责人。同一个创业实体 (以统一社会信用代码为准)或同一个创业者在残联部门只能享受一次。

                </div>

                <div class="zcyw">

                    <p class="clearfix titleBox">

                        <span class="fl">政策原文</span>

                        <a class="fl" href="../policy/fileDetail.html?type=1&id=2241f12c750445008514e8496672cd0c">关于做好残疾人一次性创业补贴有关工作的补充通知</a>

                    </p>

                    <p class="phoneBoxs">西宁市残疾人就业服务中心咨询电话：<span>0532-80979598</span></p>

                </div>

                <div class="alertBtnCont clearfix">

                    <a class="alertBtn btnSubmit fl" href="javascript:;">符合条件，立即申请</a>

                    <a class="alertBtn btnCancel fr" href="javascript:;">关闭</a>

                </div>

            </div>

        </div>

    </div>

    <!-- 残疾人 end -->

    <!-- 退役军人 start -->

    <div class="alertBg2">

        <div class="alertCont">

            <div class="alertTitle">

                退役军人创业贷款贴息扶持对象和扶持内容 

                <div class="alertClose"><img src="images/closeIcon.png" ></div>

            </div>

            <div class="alertUl scroll-bar" style="height: 600px;">

                <div class="infos">

                    <p class="titlesp">扶持对象</p>

                    户籍所在地或安置地在山东省,且在西宁市创业,有创业意愿、创业能力但创业资金有困难的退役军人。政府安排工作且在岗退役军人和专项公益性岗位在岗退役军人,不在扶持对象范围。

                    <p class="titlesp">扶持内容</p>

                    1.创业贷款贴息。对退役军人个人自主创业贷款和其创办的小微企业初创期（注册登记3年内）创业贷款，分别给予最长3年和最长2年全额贷款利率贴息。对退役军人创办的小微企业后续发展申请的信用贷款、抵押贷款，按贷款利率的50%，给予最长2年贴息。对展期、逾期的创业贷款，不予贴息。</br>

                    退役军人个人自主创业贷款、创办的小微企业初创期创业贷款、信用贷款和抵押贷款，由商业银行按规定审批办理。创业贷款的用途应当符合法律法规和国家有关规定要求，按合同约定用于创业的开办、经营等支出，不得转借他人使用或为他人担保，不得用于购买股票、期货等有价证券和从事股本权益性投资。</br>

                    （1）退役军人个人自主创业贷款和创办的小微企业初创期创业贷款。商业银行按照贷款合同签订日贷款市场报价利率，提供个人最高20万元、小微企业最高50万元的贷款额度发放创业贷款，最长3年。退役军人申请创业贷款，商业银行不得要求提供担保。不良贷款本金（含不良贷款认定前逾期利息）损失，按照国家和省、市有关要求执行。</br>

                    已申请《关于印发〈山东省创业担保贷款实施办法〉的通知》（鲁人社字〔2020〕27号）、《关于印发〈西宁市创业担保贷款实施办法〉的通知》（青人社规〔2020〕6号）规定的创业担保贷款且尚在还款期限内的，不得重复申请。</br>

                    （2）信用贷款和抵押贷款。商业银行根据退役军人自主创办小微企业经营状况、纳税记录等，按照贷款合同签订日贷款市场报价利率，提供最高150万元信用贷款，授信最长3年；有抵押资产的，贷款额度提高到最高500万元，最长3年。

                </div>

                <div class="zcyw">

                    <p class="clearfix titleBox">

                        <span class="fl">政策原文</span>

                        <a class="fl" href="../policy/fileDetail.html?type=1&id=0923d4b0ff5445cb893fa997d69b66d1">关于印发《西宁市退役军人创业扶持和困难帮扶实施细则》的通知</a>

                    </p>

                    <p class="phoneBoxs">区(市)退役军人服务中心联系电话</p>

                    <ul class="clearfix phones mt15">

                        <li class="fl">市南区：<span>88728616</span></li>

                        <li class="fl">市北区：<span>85801275</span></li>

                        <li class="fl">李沧区：<span>87656109</span></li>

                        <li class="fl">崂山区：<span>88998975</span></li>

                        <li class="fl">西海岸新区：<span>58098197</span></li>

                        <li class="fl">城阳区：<span>66737125</span></li>

                        <li class="fl">即墨区：<span>88510198</span></li>

                        <li class="fl">胶州市：<span>82200016</span></li>

                        <li class="fl">平度市：<span>58580602</span></li>

                        <li class="fl">莱西市：<span>58652928</span></li>

                    </ul>

                </div>

                <div class="alertBtnCont clearfix">

                    <a class="alertBtn btnSubmit fl" href="javascript:;">符合条件，立即申请</a>

                    <a class="alertBtn btnCancel fr" href="javascript:;">关闭</a>

                </div>

            </div>

        </div>

    </div>

    <!-- 退役军人 end -->

    <!-- 弹窗 start -->

    <div class="alertBg none">

        <div class="alertCont">

            <div class="alertTitle">

                <p>奖励措施</p>

                <div class="alertClose"><img src="images/closeIcon.png" alt="">

                </div>

            </div>

            <div class="alertUl2 scroll-bar">

                <ul>

                    <li class="mb20">一次性创业补贴1万元</li>

                    <li class="mb20">创业岗位开发补贴2000元/人</li>

                    <li class="mb20">创业担保贷款最高20万元</li>

                    <li class="mb20">财政全额贴息3年</li>

                </ul>

            </div>

        </div>

    </div>

    <!-- 弹窗 end -->

    <!-- 顶部通知栏 -->
    <div class="topNotice" style="background: #1e5aa8; color: white; padding: 8px 0; font-size: 14px; text-align: center;">
        <span>欢迎访问青创通，西宁市创业服务云平台！</span>
        <a href="javascript:;" style="color: #ffd700; margin-left: 20px;">登录</a>
        <span style="margin: 0 10px;">|</span>
        <a href="javascript:;" style="color: #ffd700;">注册</a>
    </div>

    <!-- 头部导航 -->
    <div id="headerBar" style="background: #2c7be5; position: relative;">
        <div class="conAuto1400" style="height: 80px; display: flex; align-items: center; justify-content: space-between; padding: 0 20px;">
            <div class="logo" style="display: flex; align-items: center;">
                <img src="images/logo.png" alt="青创通" style="height: 50px; margin-right: 15px;" onerror="this.style.display='none'">
                <div style="color: white;">
                    <h1 style="font-size: 24px; margin: 0; font-weight: bold;">青创通</h1>
                    <p style="font-size: 14px; margin: 0; opacity: 0.9;">西宁市创业服务云平台</p>
                </div>
            </div>
            <div class="nav" style="display: flex; align-items: center;">
                <a href="javascript:;" style="margin: 0 25px; color: white; text-decoration: none; font-size: 16px; padding: 10px 0; border-bottom: 2px solid transparent; transition: all 0.3s;">首页</a>
                <a href="javascript:;" style="margin: 0 25px; color: white; text-decoration: none; font-size: 16px; padding: 10px 0; border-bottom: 2px solid #ffd700; transition: all 0.3s;">找政策</a>
                <a href="javascript:;" style="margin: 0 25px; color: white; text-decoration: none; font-size: 16px; padding: 10px 0; border-bottom: 2px solid transparent; transition: all 0.3s;">找场地</a>
                <a href="javascript:;" style="margin: 0 25px; color: white; text-decoration: none; font-size: 16px; padding: 10px 0; border-bottom: 2px solid transparent; transition: all 0.3s;">找服务</a>
                <a href="javascript:;" style="margin: 0 25px; color: white; text-decoration: none; font-size: 16px; padding: 10px 0; border-bottom: 2px solid transparent; transition: all 0.3s;">找人才</a>
                <a href="javascript:;" style="margin: 0 25px; color: white; text-decoration: none; font-size: 16px; padding: 10px 0; border-bottom: 2px solid transparent; transition: all 0.3s;">找活动</a>
                <a href="javascript:;" style="margin: 0 25px; color: white; text-decoration: none; font-size: 16px; padding: 10px 0; border-bottom: 2px solid transparent; transition: all 0.3s;">找资讯</a>
                <a href="javascript:;" style="margin: 0 25px; color: white; text-decoration: none; font-size: 16px; padding: 10px 0; border-bottom: 2px solid transparent; transition: all 0.3s;">找服务</a>
            </div>
        </div>
    </div>

    <!-- banner部分 -->
    <div class="bannerBg" style="background: linear-gradient(135deg, #2c7be5 0%, #1e5aa8 100%); position: relative; overflow: hidden;">
        <div class="conAuto1400 pr" style="position: relative; z-index: 2;">
            <div class="bannerSlide" style="height: 300px; display: flex; align-items: center;">
                <div class="bannerContent" style="width: 100%; display: flex; align-items: center; justify-content: space-between;">
                    <div class="bannerText" style="flex: 1; color: white; padding-left: 50px;">
                        <div class="titleBox" style="margin-bottom: 30px;">
                            <h1 style="font-size: 48px; margin: 0; font-weight: bold; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">找政策</h1>
                            <p style="font-size: 20px; margin: 15px 0 0 0; opacity: 0.9;">让先海湾重创业政策，快速享受政策红利</p>
                        </div>
                        <div class="searchTabs" style="display: flex; gap: 20px; margin-bottom: 20px;">
                            <div class="tab active" style="background: rgba(255,255,255,0.2); padding: 8px 20px; border-radius: 20px; cursor: pointer;">政策</div>
                            <div class="tab" style="background: rgba(255,255,255,0.1); padding: 8px 20px; border-radius: 20px; cursor: pointer;">政策申报</div>
                            <div class="tab" style="background: rgba(255,255,255,0.1); padding: 8px 20px; border-radius: 20px; cursor: pointer;">政策解读</div>
                        </div>
                        <div class="searchBox" style="display: flex; background: white; border-radius: 25px; overflow: hidden; max-width: 500px;">
                            <input type="text" placeholder="请输入关键词搜索" style="flex: 1; border: none; padding: 12px 20px; font-size: 16px; outline: none;">
                            <button style="background: #ff6b35; color: white; border: none; padding: 12px 25px; cursor: pointer; font-size: 16px;">搜索</button>
                        </div>
                    </div>
                    <div class="bannerImage" style="flex: 1; text-align: center; position: relative;">
                        <div style="width: 300px; height: 200px; background: url('./images/policySBanner.jpg') no-repeat center; background-size: cover; border-radius: 15px; margin: 0 auto; box-shadow: 0 10px 30px rgba(0,0,0,0.3);"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 装饰性元素 -->
        <div style="position: absolute; top: -50px; right: -50px; width: 200px; height: 200px; background: rgba(255,255,255,0.1); border-radius: 50%; z-index: 1;"></div>
        <div style="position: absolute; bottom: -30px; left: -30px; width: 150px; height: 150px; background: rgba(255,255,255,0.05); border-radius: 50%; z-index: 1;"></div>
    </div>

    <!-- 弹窗 start -->

    <div class="cxPopBox width100 pr none">

        <div class="popBox pa pb30">

            <div class="titleBox width100 pr">

                <img src="images/ps_popClose.png" class="closeBtn pa cp transi">

                <p class="bTitle fb width100 tc">企业政策查询</p>

                <p class="text-white f16 width100 tc mt5">请选择您的条件，我将为您计算出适合您的政策</p>

            </div>

            <div class="sxBox width100 bc mb30">

                <div class="sxDiv width100">

                    <p class="sxTitle inlineblock f16 text-white pr">地域<img src="images/ps_popTitleIcon.png"

                            class="pa"></p>

                    <ul class="sxUl clearfix">

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="">全部</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="central">中央</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="shandong">山东省</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="qingdao">西宁市</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="shinan">市南区</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="shibei">市北区</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="licang">李沧区</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="laoshan">崂山区</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="chengyang">城阳区</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="jimo">即墨区</li>

                    </ul>

                </div>

                <div class="sxDiv width100 mt20">

                    <p class="sxTitle inlineblock f16 text-white pr">政策类型<img src="images/ps_popTitleIcon.png"

                            class="pa"></p>

                    <ul class="sxUl clearfix">

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="">全部</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="cybt">创业补贴</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="cydk">创业贷款</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="ssyh">税收优惠</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="cdzc">场地支持</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="pxbt">培训补贴</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="sbbt">社保补贴</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="rcyj">人才引进</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="kjcx">科技创新</li>

                    </ul>

                </div>

                <div class="sxDiv width100 mt20">

                    <p class="sxTitle inlineblock f16 text-white pr">政策支持<img src="images/ps_popTitleIcon.png"

                            class="pa"></p>

                    <ul class="sxUl clearfix">

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="">全部</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="zjbt">资金补贴</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="dktx">贷款贴息</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="ssjm">税收减免</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="cdzjjm">场地租金减免</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="mfpx">免费培训</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="zjzd">专家指导</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="yxsp">优先审批</li>

                    </ul>

                </div>

                <div class="sxDiv width100 mt20">

                    <p class="sxTitle inlineblock f16 text-white pr">产业分类<img src="images/ps_popTitleIcon.png"

                            class="pa"></p>

                    <ul class="sxUl clearfix">

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="">全部</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="it">信息技术</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="bio">生物医药</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="new_energy">新能源</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="manufacturing">先进制造</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="service">现代服务业</li>

                    </ul>

                </div>

                <div class="sxDiv width100 mt20">

                    <p class="sxTitle inlineblock f16 text-white pr">申报时间<img src="images/ps_popTitleIcon.png"

                            class="pa"></p>

                    <ul class="sxUl clearfix">

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="">全部</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="1">申请中</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="2">即将开始</li>

                        <li class="chooseLi fl ml10 mt20 transi tc text-gray6" name="3">已结束</li>

                    </ul>

                </div>

            </div>

            <a href="javascript:;" class="ksA bc block text-white fb tc">开始计算</a>

        </div>

    </div>

    <!-- 弹窗 end -->

    <!-- banner start -->

    <div class="bannerBg pr"></div>

    <!-- banner end -->

     <!-- box4 start -->

    <div class="box4 width100">

        <div class="conAuto1400">

            <!-- 下拉筛选 -->

            <div class="selectBox clearfix">

                <div class="fl pr mr25 selectContent">

                    <div class="selectModule clearfix module1">

                        <div class="title ">区域</div>

                        <div class="name ">

                            <p class="textoverflow">全部</p>

                        </div>

                    </div>

                    <div class=" selectList pa none">

                        <i class="arrowIcon"></i>

                        <ul>

                            <li>全部</li>

                            <li>中央</li>

                            <li>山东省</li>

                            <li>西宁市</li>

                            <li>市南区</li>

                            <li>市北区</li>

                        </ul>

                    </div>

                </div>

                <div class="fl pr mr25 selectContent">

                    <div class="selectModule clearfix module2">

                        <div class="title ">政策类型</div>

                        <div class="name ">

                            <p class="textoverflow">全部</p>

                        </div>

                    </div>

                    <div class=" selectList pa none">

                        <i class="arrowIcon"></i>

                        <ul>

                            <li>全部</li>

                            <li>创业补贴</li>

                            <li>创业贷款</li>

                            <li>税收优惠</li>

                            <li>场地支持</li>

                        </ul>

                    </div>

                </div>

                <div class="fl pr  selectContent">

                    <div class="selectModule clearfix module3">

                        <div class="title ">政策支持</div>

                        <div class="name ">

                            <p class="textoverflow">全部</p>

                        </div>

                    </div>

                    <div class=" selectList pa none">

                        <i class="arrowIcon"></i>

                        <ul>

                            <li>全部</li>

                            <li>资金补贴</li>

                            <li>贷款贴息</li>

                            <li>税收减免</li>

                            <li>场地租金减免</li>

                        </ul>

                    </div>

                </div>

            </div>

            <!-- 下拉筛选 -->

            <div class="clearfix mt40">

                <div class="leftBox fl">

                    <div class="clearfix">

                        <p class="titleP fl fb">政策<em>文件</em></p>

                        <a  href="javascript:;" class="moreBtn fr transi">

                            <span class="inlineblock text-white">更多</span>

                        </a>

                    </div>

                    <ul class="wjUl clearfix">

                        <li class="fl pr transi pr">

                            <a class="li pa transi block" target="_blank" href="javascript:;" title="西宁市创业补贴申请办法">

                                <p class="titleA textoverflow transi f18 block">西宁市创业补贴申请办法</p>

                                <div class="iconDiv clearfix width100">

                                    <p class="icon2 fl f16 text-gray9 textoverflow">创业补贴</p>

                                    <p class="icon2 fl f16 text-gray9 textoverflow">资金补贴</p>

                                    <p class="icon3 fl f16 text-gray9">2024-01-15</p>

                                </div>

                            </a>

                        </li>

                        <li class="fl pr transi pr">

                            <a class="li pa transi block" target="_blank" href="javascript:;" title="关于进一步支持大学生创业的若干措施">

                                <p class="titleA textoverflow transi f18 block">关于进一步支持大学生创业的若干措施</p>

                                <div class="iconDiv clearfix width100">

                                    <p class="icon2 fl f16 text-gray9 textoverflow">创业贷款</p>

                                    <p class="icon2 fl f16 text-gray9 textoverflow">贷款贴息</p>

                                    <p class="icon3 fl f16 text-gray9">2024-01-10</p>

                                </div>

                            </a>

                        </li>

                        <li class="fl pr transi pr">

                            <a class="li pa transi block" target="_blank" href="javascript:;" title="西宁市小微企业税收优惠政策实施细则">

                                <p class="titleA textoverflow transi f18 block">西宁市小微企业税收优惠政策实施细则</p>

                                <div class="iconDiv clearfix width100">

                                    <p class="icon2 fl f16 text-gray9 textoverflow">税收优惠</p>

                                    <p class="icon2 fl f16 text-gray9 textoverflow">税收减免</p>

                                    <p class="icon3 fl f16 text-gray9">2024-01-08</p>

                                </div>

                            </a>

                        </li>

                    </ul>

                </div>

                <div class="rightBox fr" style="width: 400px;">

                    <div class="clearfix" style="margin-bottom: 20px;">
                        <p class="title fl" style="font-size: 24px; font-weight: bold; color: #333;">微短剧</p>
                        <a href="javascript:;" class="moreBtn fr transi" style="color: #2c7be5; text-decoration: none; font-size: 14px; line-height: 30px;">
                            <span class="inlineblock">更多</span>
                        </a>
                    </div>

                    <div class="videoCarousel" style="position: relative; overflow: hidden; border-radius: 10px; background: #f8f9fa;">

                        <!-- 轮播容器 -->
                        <div class="carouselContainer" style="position: relative; height: 280px;">

                            <!-- 轮播项目1 -->
                            <div class="carouselItem active" style="position: absolute; width: 100%; height: 100%; opacity: 1; transition: opacity 0.5s ease;">
                                <a href="javascript:;" style="display: block; height: 100%; text-decoration: none; color: inherit;">
                                    <div style="position: relative; height: 180px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center;">
                                        <div style="text-align: center; color: white;">
                                            <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                                <span style="font-size: 24px;">▶</span>
                                            </div>
                                            <p style="font-size: 16px; margin: 0;">创业补贴申请流程详解</p>
                                        </div>
                                    </div>
                                    <div style="padding: 15px;">
                                        <h4 style="margin: 0 0 8px 0; font-size: 16px; color: #333;">创业补贴申请流程详解</h4>
                                        <p style="margin: 0; font-size: 14px; color: #666; line-height: 1.4;">详细介绍西宁市创业补贴的申请条件、流程和注意事项</p>
                                    </div>
                                </a>
                            </div>

                            <!-- 轮播项目2 -->
                            <div class="carouselItem" style="position: absolute; width: 100%; height: 100%; opacity: 0; transition: opacity 0.5s ease;">
                                <a href="javascript:;" style="display: block; height: 100%; text-decoration: none; color: inherit;">
                                    <div style="position: relative; height: 180px; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); display: flex; align-items: center; justify-content: center;">
                                        <div style="text-align: center; color: white;">
                                            <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                                <span style="font-size: 24px;">▶</span>
                                            </div>
                                            <p style="font-size: 16px; margin: 0;">大学生创业政策解读</p>
                                        </div>
                                    </div>
                                    <div style="padding: 15px;">
                                        <h4 style="margin: 0 0 8px 0; font-size: 16px; color: #333;">大学生创业政策解读</h4>
                                        <p style="margin: 0; font-size: 14px; color: #666; line-height: 1.4;">针对大学生群体的创业扶持政策详细解读</p>
                                    </div>
                                </a>
                            </div>

                            <!-- 轮播项目3 -->
                            <div class="carouselItem" style="position: absolute; width: 100%; height: 100%; opacity: 0; transition: opacity 0.5s ease;">
                                <a href="javascript:;" style="display: block; height: 100%; text-decoration: none; color: inherit;">
                                    <div style="position: relative; height: 180px; background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); display: flex; align-items: center; justify-content: center;">
                                        <div style="text-align: center; color: white;">
                                            <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px;">
                                                <span style="font-size: 24px;">▶</span>
                                            </div>
                                            <p style="font-size: 16px; margin: 0;">小微企业扶持政策介绍</p>
                                        </div>
                                    </div>
                                    <div style="padding: 15px;">
                                        <h4 style="margin: 0 0 8px 0; font-size: 16px; color: #333;">小微企业扶持政策介绍</h4>
                                        <p style="margin: 0; font-size: 14px; color: #666; line-height: 1.4;">小微企业可享受的各类扶持政策和优惠措施</p>
                                    </div>
                                </a>
                            </div>

                        </div>

                        <!-- 轮播指示器 -->
                        <div class="carouselIndicators" style="position: absolute; bottom: 10px; left: 50%; transform: translateX(-50%); display: flex; gap: 8px;">
                            <div class="indicator active" data-slide="0" style="width: 8px; height: 8px; border-radius: 50%; background: #2c7be5; cursor: pointer; transition: all 0.3s;"></div>
                            <div class="indicator" data-slide="1" style="width: 8px; height: 8px; border-radius: 50%; background: rgba(255,255,255,0.5); cursor: pointer; transition: all 0.3s;"></div>
                            <div class="indicator" data-slide="2" style="width: 8px; height: 8px; border-radius: 50%; background: rgba(255,255,255,0.5); cursor: pointer; transition: all 0.3s;"></div>
                        </div>

                        <!-- 轮播控制按钮 -->
                        <div class="carouselControls">
                            <button class="prevBtn" style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); background: rgba(0,0,0,0.5); color: white; border: none; width: 30px; height: 30px; border-radius: 50%; cursor: pointer; font-size: 14px;">‹</button>
                            <button class="nextBtn" style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: rgba(0,0,0,0.5); color: white; border: none; width: 30px; height: 30px; border-radius: 50%; cursor: pointer; font-size: 14px;">›</button>
                        </div>

                    </div>

                </div>

            </div>

            <a href="javascript:;" class="calculateBtn"></a>

        </div>

    </div>

    <!-- box4 end -->

    <!-- box2 start -->

    <div class="box2 width100">

        <div class="conAuto1400">

            <div class="clearfix">

                <p class="titleP fl fb">政策<em>申报</em></p>

                <a target="_blank" href="javascript:;" class="moreBtn fr transi">

                    <span class="inlineblock text-white">更多</span>

                </a>

            </div>



            <div class="clearfix pr width100">

                <!-- 轮播控制按钮已移除 -->

                <div class="bd">

                    <ul class="sbUl clearfix">

                        <li class="fl transi">

                            <a href="javascript:;" target="_blank" class="topBox width100 block">

                                <div class="clearfix">

                                    <span class="sqSpan icon1 fl text-white tc">申请中</span>

                                    <p class="linkA fl ml10 textoverflow transi f20" title="2024年西宁市创业补贴申报">2024年西宁市创业补贴申报</p>

                                </div>

                                <div class="clearfix">

                                    <div class="lBox fl">

                                        <p class="fwP width100 text-gray6 textoverflow">西宁市人力资源和社会保障局</p>

                                        <p class="timeP width100 text-gray6 textoverflow">2024-01-01 至 2024-12-31</p>

                                        <div class="spanBox clearfix mt15 width100 textoverflow">

                                            <span class="fl textoverflow">创业补贴</span>

                                            <span class="fl textoverflow">西宁市</span>

                                        </div>

                                    </div>

                                    <div class="numBox fr tc on">

                                        <div class="inlineblock">

                                            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00">10</span><span>万元</span></p>

                                            <p class="tc width100 text-gray9 mt5">最高扶持</p>

                                        </div>

                                        <div class="inlineblock detailStatusName">申请中</div>

                                    </div>

                                </div>

                            </a>

                            <div class="bottomBox width100 tc">

                                <a href="javascript:;" class="inlineblock f15 text-0050ff">奖励措施</a>

                                <span class="inlineblock f15 text-grayB ml10 mr10">|</span>

                                <a target="_blank" href="javascript:;" class="inlineblock f15 text-0050ff">查看申报通知</a>

                            </div>

                        </li>

                        <li class="fl transi">

                            <a href="javascript:;" target="_blank" class="topBox width100 block">

                                <div class="clearfix">

                                    <span class="sqSpan icon1 fl text-white tc">申请中</span>

                                    <p class="linkA fl ml10 textoverflow transi f20" title="大学生创业担保贷款申请">大学生创业担保贷款申请</p>

                                </div>

                                <div class="clearfix">

                                    <div class="lBox fl">

                                        <p class="fwP width100 text-gray6 textoverflow">西宁市财政局</p>

                                        <p class="timeP width100 text-gray6 textoverflow">2024-02-01 至 2024-11-30</p>

                                        <div class="spanBox clearfix mt15 width100 textoverflow">

                                            <span class="fl textoverflow">创业贷款</span>

                                            <span class="fl textoverflow">西宁市</span>

                                        </div>

                                    </div>

                                    <div class="numBox fr tc on">

                                        <div class="inlineblock">

                                            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00">贷款贴息</span></p>

                                            <p class="tc width100 text-gray9 mt5">支持方式</p>

                                        </div>

                                        <div class="inlineblock detailStatusName">申请中</div>

                                    </div>

                                </div>

                            </a>

                            <div class="bottomBox width100 tc">

                                <a href="javascript:;" class="inlineblock f15 text-0050ff">奖励措施</a>

                                <span class="inlineblock f15 text-grayB ml10 mr10">|</span>

                                <a target="_blank" href="javascript:;" class="inlineblock f15 text-0050ff">查看申报通知</a>

                            </div>

                        </li>

                        <li class="fl transi">

                            <a href="javascript:;" target="_blank" class="topBox width100 block">

                                <div class="clearfix">

                                    <span class="sqSpan icon2 fl text-white tc">待开始</span>

                                    <p class="linkA fl ml10 textoverflow transi f20" title="小微企业税收减免申请">小微企业税收减免申请</p>

                                </div>

                                <div class="clearfix">

                                    <div class="lBox fl">

                                        <p class="fwP width100 text-gray6 textoverflow">西宁市税务局</p>

                                        <p class="timeP width100 text-gray6 textoverflow">2024-03-01 至 2024-10-31</p>

                                        <div class="spanBox clearfix mt15 width100 textoverflow">

                                            <span class="fl textoverflow">税收优惠</span>

                                            <span class="fl textoverflow">西宁市</span>

                                        </div>

                                    </div>

                                    <div class="numBox fr tc on">

                                        <div class="inlineblock">

                                            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00">税收减免</span></p>

                                            <p class="tc width100 text-gray9 mt5">支持方式</p>

                                        </div>

                                        <div class="inlineblock detailStatusName">待开始</div>

                                    </div>

                                </div>

                            </a>

                            <div class="bottomBox width100 tc">

                                <a href="javascript:;" class="inlineblock f15 text-0050ff">奖励措施</a>

                                <span class="inlineblock f15 text-grayB ml10 mr10">|</span>

                                <a target="_blank" href="javascript:;" class="inlineblock f15 text-0050ff">查看申报通知</a>

                            </div>

                        </li>

                        <li class="fl transi">

                            <a href="javascript:;" target="_blank" class="topBox width100 block">

                                <div class="clearfix">

                                    <span class="sqSpan icon1 fl text-white tc">申请中</span>

                                    <p class="linkA fl ml10 textoverflow transi f20" title="高校毕业生就业创业补贴">高校毕业生就业创业补贴</p>

                                </div>

                                <div class="clearfix">

                                    <div class="lBox fl">

                                        <p class="fwP width100 text-gray6 textoverflow">西宁市教育局</p>

                                        <p class="timeP width100 text-gray6 textoverflow">2024-04-01 至 2024-12-31</p>

                                        <div class="spanBox clearfix mt15 width100 textoverflow">

                                            <span class="fl textoverflow">就业补贴</span>

                                            <span class="fl textoverflow">西宁市</span>

                                        </div>

                                    </div>

                                    <div class="numBox fr tc on">

                                        <div class="inlineblock">

                                            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00">5</span><span>万元</span></p>

                                            <p class="tc width100 text-gray9 mt5">最高扶持</p>

                                        </div>

                                        <div class="inlineblock detailStatusName">申请中</div>

                                    </div>

                                </div>

                            </a>

                            <div class="bottomBox width100 tc">

                                <a href="javascript:;" class="inlineblock f15 text-0050ff">奖励措施</a>

                                <span class="inlineblock f15 text-grayB ml10 mr10">|</span>

                                <a target="_blank" href="javascript:;" class="inlineblock f15 text-0050ff">查看申报通知</a>

                            </div>

                        </li>

                        <li class="fl transi">

                            <a href="javascript:;" target="_blank" class="topBox width100 block">

                                <div class="clearfix">

                                    <span class="sqSpan icon2 fl text-white tc">待开始</span>

                                    <p class="linkA fl ml10 textoverflow transi f20" title="科技型中小企业研发费用补助">科技型中小企业研发费用补助</p>

                                </div>

                                <div class="clearfix">

                                    <div class="lBox fl">

                                        <p class="fwP width100 text-gray6 textoverflow">西宁市科技局</p>

                                        <p class="timeP width100 text-gray6 textoverflow">2024-05-01 至 2024-09-30</p>

                                        <div class="spanBox clearfix mt15 width100 textoverflow">

                                            <span class="fl textoverflow">科技创新</span>

                                            <span class="fl textoverflow">西宁市</span>

                                        </div>

                                    </div>

                                    <div class="numBox fr tc on">

                                        <div class="inlineblock">

                                            <p class="tc width100 text-gray9"><span class="fb f24 text-ff8a00">研发补助</span></p>

                                            <p class="tc width100 text-gray9 mt5">支持方式</p>

                                        </div>

                                        <div class="inlineblock detailStatusName">待开始</div>

                                    </div>

                                </div>

                            </a>

                            <div class="bottomBox width100 tc">

                                <a href="javascript:;" class="inlineblock f15 text-0050ff">奖励措施</a>

                                <span class="inlineblock f15 text-grayB ml10 mr10">|</span>

                                <a target="_blank" href="javascript:;" class="inlineblock f15 text-0050ff">查看申报通知</a>

                            </div>

                        </li>

                    </ul>

                </div>

            </div>



        </div>

    </div>

    <!-- box2 end -->

    <!-- 联办 start -->

    <div class="lbOut">

        <div class="conAuto1400 pr" style="height: 276px;">

            <div class="clearfix">

                <p class="titleP fl fb">部门<em>联办</em></p>

            </div>

            <ul class="clearfix">

                <!-- <li class="fl transi">

                    <a href="./tip.html?id=1" title="操作说明" target="_blank" class="tips"></a>

                    <div class="icon1">

                        <p>创业补贴申请</p>

                        <a class="transi" href="javascript:;">立即申请</a>

                    </div>

                </li> -->

                <li class="fl transi">

                    <div class="icon7">

                        <p>个人创业一件事</p>

                        <a class="transi" target="_blank" href="https://hrss.qingdao.gov.cn/cy/entrepreneurship/index.html?page=home">立即查看</a>

                    </div>

                </li>

                <li class="fl transi">

                    <a href="./tip.html?id=2" title="操作说明" target="_blank" class="tips"></a>

                    <div class="icon3">

                        <p>创业担保贷款申请</p>

                        <div class="clearfix bc" style="width: 170px;">

                            <a class="transi fl" href="javascript:;">立即申请</a>

                            <a class="transi fr" style="color: #ff6000;" href="javascript:;">办事指南</a>

                        </div>

                    </div>

                </li>

                <!-- <li class="fl transi">

                    <a href="./tip.html?id=3" title="操作说明" target="_blank" class="tips"></a>

                    <div class="icon6">

                        <p>创办企业、变更备案、</br>企业注销</p>

                        <a class="transi"  href="javascript:;">立即申请</a>

                    </div>

                </li> -->

                <li class="fl transi">

                    <div class="icon2">

                        <p>残疾人一次性创业</br>补贴申请</p>

                        <a class="transi"  href="javascript:;">立即申请</a>

                    </div>

                </li>

                <li class="fl transi">

                    <div class="icon5">

                        <p>退役军人创业贴息</br>贷款政策申请</p>

                        <a class="transi" href="javascript:;">立即申请</a>

                    </div>

                </li>

                <li class="fl transi">

                    <a href="./tip.html?id=4" title="操作说明" target="_blank" class="tips"></a>

                    <div class="icon4">

                        <p>融资贷款申请</p>

                        <a class="transi" target="_blank" href="https://qdceloan.com/#/homeIndex">立即申请</a>

                    </div>

                </li>

            </ul>

        </div>

    </div>

    <!-- 联办 end -->

    <!-- box7 start -->

    <div class="box7 width100">

        <div class="conAuto1400">

            <div class="clearfix">

                <a href="http://rs.qdqss.net/" target="_blank" class="fl">

                    <img src="images/policySImg2.png" class="banner2 block">

                </a>

                <div class="rightBox fr pr">

                    <div class="clearfix">

                        <p class="titleP fl fb" style="padding-bottom: 9px;">政策<em>解读</em></p>

                        <a href="javascript:;" class="moreBtn fr transi">

                            <span class="inlineblock text-white">更多</span>

                        </a>

                    </div>

                    <div class="clearfix">

                        <div class="videoBox clearfix">

                            <a class="fl transi" href="javascript:;">

                                <div class="imgSize width100 pr">

                                    <p class="pa paraoverflow3" title="创业补贴政策解读：如何申请一次性创业补贴">创业补贴政策解读：如何申请一次性创业补贴</p>

                                    <img class="transi pa" src="./images/pic_noDetail.png">

                                </div>

                                <p class="liTitle mt15 f18 paraoverflow2 transi" title="创业补贴政策解读：如何申请一次性创业补贴">创业补贴政策解读：如何申请一次性创业补贴</p>

                                <div class="clearfix mt15">

                                    <p class="linkP fl f15 text-gray9 textoverflow">西宁市人社局</p>

                                    <p class="timeP fr f15 text-gray9">2024-01-20</p>

                                </div>

                            </a>

                            <a class="fl transi" href="javascript:;">

                                <div class="imgSize width100 pr">

                                    <p class="pa paraoverflow3" title="大学生创业贷款政策详解及申请流程">大学生创业贷款政策详解及申请流程</p>

                                    <img class="transi pa" src="./images/pic_noDetail.png">

                                </div>

                                <p class="liTitle mt15 f18 paraoverflow2 transi" title="大学生创业贷款政策详解及申请流程">大学生创业贷款政策详解及申请流程</p>

                                <div class="clearfix mt15">

                                    <p class="linkP fl f15 text-gray9 textoverflow">西宁市财政局</p>

                                    <p class="timeP fr f15 text-gray9">2024-01-18</p>

                                </div>

                            </a>

                        </div>

                    </div>

                </div>

            </div>

            

        </div>

    </div>

    <!-- box7 end -->

    <!-- 页脚 -->
    <div id="footerBar">
        <div style="background: #f8f9fa; padding: 40px 0; border-top: 1px solid #eee;">
            <div class="conAuto1400" style="text-align: center;">
                <div style="margin-bottom: 20px;">
                    <a href="javascript:;" style="margin: 0 20px; color: #666; text-decoration: none;">关于我们</a>
                    <a href="javascript:;" style="margin: 0 20px; color: #666; text-decoration: none;">联系我们</a>
                    <a href="javascript:;" style="margin: 0 20px; color: #666; text-decoration: none;">帮助中心</a>
                    <a href="javascript:;" style="margin: 0 20px; color: #666; text-decoration: none;">意见反馈</a>
                </div>
                <p style="color: #999; font-size: 14px; margin: 10px 0;">西宁市创业政策服务平台</p>
                <p style="color: #999; font-size: 12px;">© 2024 西宁市人力资源和社会保障局 版权所有</p>
            </div>
        </div>
    </div>

    <!-- 所有JavaScript文件已移除，仅保留CSS样式和静态内容 -->

    <!-- 添加基本的交互功能 -->
    <script>
        // 页面加载完成后绑定基本事件
        document.addEventListener('DOMContentLoaded', function() {
            // 阻止所有 javascript: 链接的默认行为
            preventJavaScriptLinks();

            // 初始化部门联办动画
            initDepartmentAnimation();

            // 初始化轮播功能
            initCarousel();

            // 初始化导航悬停效果
            initNavHover();

            console.log('页面已加载完成，已阻止javascript:链接跳转');
        });

        // 阻止 javascript: 链接跳转
        function preventJavaScriptLinks() {
            // 获取所有 href="javascript:;" 的链接
            var jsLinks = document.querySelectorAll('a[href="javascript:;"]');

            jsLinks.forEach(function(link) {
                link.addEventListener('click', function(event) {
                    // 阻止默认的跳转行为
                    event.preventDefault();
                    event.stopPropagation();

                    // 可以在这里添加自定义的处理逻辑
                    console.log('阻止了链接跳转:', this.textContent.trim());

                    // 如果需要，可以添加一些视觉反馈
                    this.style.opacity = '0.7';
                    setTimeout(() => {
                        this.style.opacity = '1';
                    }, 200);

                    return false;
                });
            });

            console.log('已处理 ' + jsLinks.length + ' 个 javascript: 链接');
        }

        // 初始化轮播功能
        function initCarousel() {
            var currentSlide = 0;
            var slides = document.querySelectorAll('.carouselItem');
            var indicators = document.querySelectorAll('.indicator');
            var prevBtn = document.querySelector('.prevBtn');
            var nextBtn = document.querySelector('.nextBtn');
            var totalSlides = slides.length;

            if (totalSlides === 0) return;

            // 显示指定的幻灯片
            function showSlide(index) {
                // 隐藏所有幻灯片
                slides.forEach(function(slide) {
                    slide.style.opacity = '0';
                });

                // 移除所有指示器的活动状态
                indicators.forEach(function(indicator) {
                    indicator.style.background = 'rgba(255,255,255,0.5)';
                    indicator.classList.remove('active');
                });

                // 显示当前幻灯片
                if (slides[index]) {
                    slides[index].style.opacity = '1';
                }

                // 激活当前指示器
                if (indicators[index]) {
                    indicators[index].style.background = '#2c7be5';
                    indicators[index].classList.add('active');
                }
            }

            // 下一张
            function nextSlide() {
                currentSlide = (currentSlide + 1) % totalSlides;
                showSlide(currentSlide);
            }

            // 上一张
            function prevSlide() {
                currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
                showSlide(currentSlide);
            }

            // 绑定按钮事件
            if (nextBtn) {
                nextBtn.addEventListener('click', nextSlide);
            }

            if (prevBtn) {
                prevBtn.addEventListener('click', prevSlide);
            }

            // 绑定指示器事件
            indicators.forEach(function(indicator, index) {
                indicator.addEventListener('click', function() {
                    currentSlide = index;
                    showSlide(currentSlide);
                });
            });

            // 自动轮播
            setInterval(nextSlide, 4000);
        }

        // 初始化导航悬停效果
        function initNavHover() {
            var navLinks = document.querySelectorAll('#headerBar .nav a');

            navLinks.forEach(function(link) {
                link.addEventListener('mouseenter', function() {
                    this.style.borderBottomColor = '#ffd700';
                    this.style.transform = 'translateY(-2px)';
                });

                link.addEventListener('mouseleave', function() {
                    if (!this.style.borderBottomColor.includes('#ffd700') || this.textContent.trim() !== '找政策') {
                        this.style.borderBottomColor = 'transparent';
                    }
                    this.style.transform = 'translateY(0)';
                });
            });
        }

        // 部门联办动画效果
        function initDepartmentAnimation() {
            var departmentItems = document.querySelectorAll('.lbOut ul li');

            departmentItems.forEach(function(item) {
                item.addEventListener('mouseenter', function() {
                    // 移除其他项目的on类
                    departmentItems.forEach(function(otherItem) {
                        otherItem.classList.remove('on');
                    });
                    // 添加当前项目的on类
                    this.classList.add('on');
                });

                item.addEventListener('mouseleave', function() {
                    // 鼠标离开时移除on类
                    this.classList.remove('on');
                });
            });
        }
    </script>

</body>



</html>